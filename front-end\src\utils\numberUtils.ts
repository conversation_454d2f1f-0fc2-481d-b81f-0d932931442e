/**
 * Utility functions for safe number operations
 * Handles cases where API returns strings instead of numbers
 */

/**
 * Safely converts a value to number, returns 0 if invalid
 */
export const safeNumber = (value: any): number => {
  if (value === null || value === undefined || value === '') {
    return 0;
  }
  
  const num = Number(value);
  return isNaN(num) ? 0 : num;
};

/**
 * Safely formats a number with toFixed, handles string inputs
 */
export const safeToFixed = (value: any, digits: number = 2): string => {
  return safeNumber(value).toFixed(digits);
};

/**
 * Safely formats a number with toLocaleString, handles string inputs
 */
export const safeToLocaleString = (value: any, locale: string = 'id-ID'): string => {
  return safeNumber(value).toLocaleString(locale);
};

/**
 * Safely formats currency, handles string inputs
 */
export const safeCurrency = (value: any, locale: string = 'id-ID', currency: string = 'IDR'): string => {
  return new Intl.NumberFormat(locale, {
    style: 'currency',
    currency: currency,
    minimumFractionDigits: 0
  }).format(safeNumber(value));
};

/**
 * Safely calculates percentage, handles string inputs
 */
export const safePercentage = (current: any, total: any): number => {
  const currentNum = safeNumber(current);
  const totalNum = safeNumber(total);
  
  if (totalNum === 0) return 0;
  return (currentNum / totalNum) * 100;
};

/**
 * Safely gets minimum value between two numbers, handles string inputs
 */
export const safeMin = (a: any, b: any): number => {
  return Math.min(safeNumber(a), safeNumber(b));
};

/**
 * Safely gets maximum value between two numbers, handles string inputs
 */
export const safeMax = (a: any, b: any): number => {
  return Math.max(safeNumber(a), safeNumber(b));
};

/**
 * Safely formats date string
 */
export const safeDate = (dateString: any): string => {
  if (!dateString) return '-';
  
  try {
    const date = new Date(dateString);
    if (isNaN(date.getTime())) return '-';
    return date.toLocaleDateString('id-ID');
  } catch {
    return '-';
  }
};

/**
 * Safely formats datetime string
 */
export const safeDateTime = (dateString: any): string => {
  if (!dateString) return '-';
  
  try {
    const date = new Date(dateString);
    if (isNaN(date.getTime())) return '-';
    return date.toLocaleDateString('id-ID', {
      year: 'numeric',
      month: 'long',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  } catch {
    return '-';
  }
};
