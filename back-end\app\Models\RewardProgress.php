<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Carbon\Carbon;

class RewardProgress extends Model
{
    use HasFactory;

    protected $table = 'reward_progresses';

    protected $fillable = [
        'reward_id',
        'mitra_id',
        'current_quantity',
        'target_quantity',
        'progress_percentage',
        'status',
        'completed_at',
        'purchase_history',
        'notes'
    ];

    protected $casts = [
        'current_quantity' => 'integer',
        'target_quantity' => 'integer',
        'progress_percentage' => 'decimal:2',
        'completed_at' => 'datetime',
        'purchase_history' => 'array',
    ];

    /**
     * <PERSON>lasi ke Reward
     */
    public function reward()
    {
        return $this->belongsTo(Reward::class);
    }

    /**
     * Relasi ke Mitra
     */
    public function mitra()
    {
        return $this->belongsTo(Mitra::class);
    }

    /**
     * Scope untuk progress yang sedang berjalan
     */
    public function scopeInProgress($query)
    {
        return $query->where('status', 'in_progress');
    }

    /**
     * Scope untuk progress yang sudah selesai
     */
    public function scopeCompleted($query)
    {
        return $query->where('status', 'completed');
    }

    /**
     * Scope untuk owner tertentu
     */
    public function scopeForOwner($query, $ownerId)
    {
        return $query->whereHas('reward.creator', function ($q) use ($ownerId) {
            $q->where(function ($ownerScope) use ($ownerId) {
                $ownerScope->where('id', $ownerId)
                    ->orWhere('owner_id', $ownerId);
            });
        });
    }

    /**
     * Accessor untuk status yang user-friendly
     */
    public function getStatusNameAttribute(): string
    {
        $statusNames = [
            'in_progress' => 'Sedang Berjalan',
            'completed' => 'Selesai',
            'expired' => 'Kedaluwarsa'
        ];

        return $statusNames[$this->status] ?? $this->status;
    }

    /**
     * Accessor untuk sisa quantity
     */
    public function getRemainingQuantityAttribute(): int
    {
        return max(0, $this->target_quantity - $this->current_quantity);
    }

    /**
     * Accessor untuk progress dalam format yang mudah dibaca
     */
    public function getProgressTextAttribute(): string
    {
        return "{$this->current_quantity} / {$this->target_quantity} ({$this->progress_percentage}%)";
    }

    /**
     * Cek apakah sudah mencapai target
     */
    public function hasReachedTarget(): bool
    {
        return $this->current_quantity >= $this->target_quantity;
    }

    /**
     * Update progress berdasarkan quantity baru
     */
    public function updateProgress(int $additionalQuantity, array $transactionData = []): bool
    {
        $this->current_quantity += $additionalQuantity;

        // Hitung persentase progress
        $this->progress_percentage = $this->target_quantity > 0
            ? min(100, ($this->current_quantity / $this->target_quantity) * 100)
            : 0;

        // Update purchase history
        $history = $this->purchase_history ?? [];
        $history[] = [
            'date' => now()->toDateString(),
            'quantity' => $additionalQuantity,
            'total_quantity' => $this->current_quantity,
            'transaction_data' => $transactionData,
            'created_at' => now()->toISOString()
        ];
        $this->purchase_history = $history;

        // Cek apakah sudah mencapai target
        if ($this->hasReachedTarget() && $this->status === 'in_progress') {
            $this->status = 'completed';
            $this->completed_at = now();
        }

        return $this->save();
    }

    /**
     * Get history pembelian dalam format yang mudah dibaca
     */
    public function getFormattedPurchaseHistory(): array
    {
        $history = $this->purchase_history ?? [];

        return collect($history)->map(function ($item) {
            return [
                'date' => Carbon::parse($item['date'])->format('d/m/Y'),
                'quantity' => $item['quantity'],
                'total_quantity' => $item['total_quantity'],
                'transaction_id' => $item['transaction_data']['transaction_id'] ?? null,
                'created_at' => Carbon::parse($item['created_at'])->format('d/m/Y H:i')
            ];
        })->toArray();
    }

    /**
     * Get total pembelian dalam periode tertentu
     */
    public function getPurchaseInPeriod(Carbon $startDate, Carbon $endDate): int
    {
        $history = $this->purchase_history ?? [];

        return collect($history)
            ->filter(function ($item) use ($startDate, $endDate) {
                $purchaseDate = Carbon::parse($item['date']);
                return $purchaseDate->between($startDate, $endDate);
            })
            ->sum('quantity');
    }

    /**
     * Get statistik progress
     */
    public function getStatistics(): array
    {
        $history = $this->purchase_history ?? [];
        $totalTransactions = count($history);

        $averagePerTransaction = $totalTransactions > 0
            ? round($this->current_quantity / $totalTransactions, 2)
            : 0;

        $firstPurchase = collect($history)->first();
        $lastPurchase = collect($history)->last();

        $daysSinceStart = $firstPurchase
            ? Carbon::parse($firstPurchase['date'])->diffInDays(now()) + 1
            : 0;

        $averagePerDay = $daysSinceStart > 0
            ? round($this->current_quantity / $daysSinceStart, 2)
            : 0;

        return [
            'current_quantity' => $this->current_quantity,
            'target_quantity' => $this->target_quantity,
            'remaining_quantity' => $this->remaining_quantity,
            'progress_percentage' => $this->progress_percentage,
            'total_transactions' => $totalTransactions,
            'average_per_transaction' => $averagePerTransaction,
            'average_per_day' => $averagePerDay,
            'days_since_start' => $daysSinceStart,
            'is_completed' => $this->status === 'completed',
            'completed_at' => $this->completed_at?->format('d/m/Y H:i'),
            'first_purchase' => $firstPurchase ? Carbon::parse($firstPurchase['date'])->format('d/m/Y') : null,
            'last_purchase' => $lastPurchase ? Carbon::parse($lastPurchase['date'])->format('d/m/Y') : null
        ];
    }

    /**
     * Estimasi kapan akan selesai berdasarkan rata-rata pembelian
     */
    public function getEstimatedCompletionDate(): ?Carbon
    {
        if ($this->status === 'completed') {
            return Carbon::parse($this->completed_at);
        }

        $history = $this->purchase_history ?? [];
        if (count($history) < 2) {
            return null;
        }

        $firstPurchase = collect($history)->first();
        $daysSinceStart = Carbon::parse($firstPurchase['date'])->diffInDays(now()) + 1;

        if ($daysSinceStart <= 0) {
            return null;
        }

        $averagePerDay = $this->current_quantity / $daysSinceStart;

        if ($averagePerDay <= 0) {
            return null;
        }

        $remainingDays = ceil($this->remaining_quantity / $averagePerDay);

        return now()->addDays($remainingDays);
    }

    /**
     * Boot method untuk event handling
     */
    protected static function boot()
    {
        parent::boot();

        // Event setelah update
        static::updated(function ($progress) {
            // Jika status berubah menjadi completed, bisa trigger notifikasi
            if ($progress->wasChanged('status') && $progress->status === 'completed') {
                // TODO: Trigger notification atau event lainnya
            }
        });
    }
}
