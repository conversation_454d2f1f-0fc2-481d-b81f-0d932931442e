<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('rewards', function (Blueprint $table) {
            $table->id();
            $table->string('name'); // Nama reward (Paket Haji, Motor, dll)
            $table->text('description')->nullable(); // Deskripsi reward
            $table->foreignId('category_id')->constrained('categories')->cascadeOnDelete(); // Kategori produk
            $table->foreignId('product_id')->nullable()->constrained('products')->cascadeOnDelete(); // Produk spesifik (opsional)
            $table->integer('target_quantity'); // Target penjualan (1000 pcs)
            $table->date('start_date'); // Tanggal mulai program
            $table->date('end_date'); // Tanggal berakhir program
            $table->enum('status', ['active', 'inactive', 'completed', 'expired'])->default('active'); // Status reward
            $table->text('terms_conditions')->nullable(); // Syarat dan ketentuan
            $table->string('reward_image')->nullable(); // Gambar reward
            $table->decimal('reward_value', 15, 2)->nullable(); // Nilai reward dalam rupiah
            $table->foreignId('created_by')->constrained('users')->cascadeOnDelete(); // Owner yang membuat
            $table->timestamps();
            
            // Index untuk performa
            $table->index(['status', 'start_date', 'end_date']);
            $table->index(['category_id', 'product_id']);
            $table->index('created_by');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('rewards');
    }
};
