<?php

namespace Database\Seeders;

use App\Models\User;
use App\Models\Category;
use App\Models\Product;
use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\Hash;

class ProductSyncTestSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Buat Super Admin
        $superAdmin = User::create([
            'name' => 'Super Admin',
            'email' => '<EMAIL>',
            'phone' => '628123456789',
            'role' => 'superadmin',
            'password' => Hash::make('password'),
        ]);

        // Buat Owner 1
        $owner1 = User::create([
            'name' => 'Owner Toko A',
            'email' => '<EMAIL>',
            'phone' => '628111000001',
            'role' => 'owner',
            'password' => Hash::make('password'),
        ]);

        // Buat Owner 2
        $owner2 = User::create([
            'name' => 'Owner Toko B',
            'email' => '<EMAIL>',
            'phone' => '628111000002',
            'role' => 'owner',
            'password' => Hash::make('password'),
        ]);

        // Buat Admin Gudang untuk Owner 1
        $adminGudang1 = User::create([
            'name' => 'Admin Gudang Toko A',
            'email' => '<EMAIL>',
            'phone' => '628222000001',
            'role' => 'admin_gudang',
            'owner_id' => $owner1->id,
            'password' => Hash::make('password'),
        ]);

        // Buat Admin Gudang untuk Owner 2
        $adminGudang2 = User::create([
            'name' => 'Admin Gudang Toko B',
            'email' => '<EMAIL>',
            'phone' => '628222000002',
            'role' => 'admin_gudang',
            'owner_id' => $owner2->id,
            'password' => Hash::make('password'),
        ]);

        // Buat kategori oleh super admin
        $categoryPupuk = Category::create([
            'name' => 'Pupuk',
            'description' => 'Kategori untuk produk pupuk',
            'created_by' => $superAdmin->id,
        ]);

        $categoryPestisida = Category::create([
            'name' => 'Pestisida',
            'description' => 'Kategori untuk produk pestisida',
            'created_by' => $superAdmin->id,
        ]);

        // Buat kategori oleh owner 1
        $categoryBenih = Category::create([
            'name' => 'Benih',
            'description' => 'Kategori untuk produk benih',
            'created_by' => $owner1->id,
        ]);

        // Buat produk oleh super admin (akan terlihat oleh semua admin gudang)
        Product::create([
            'name' => 'Pupuk NPK Global',
            'category_id' => $categoryPupuk->id,
            'price' => 85000,
            'hpp' => 75000,
            'stock' => 200,
            'unit' => 'kg',
            'description' => 'Pupuk NPK berkualitas tinggi dari super admin',
            'created_by' => $superAdmin->id,
        ]);

        Product::create([
            'name' => 'Pestisida Universal',
            'category_id' => $categoryPestisida->id,
            'price' => 120000,
            'hpp' => 100000,
            'stock' => 50,
            'unit' => 'liter',
            'description' => 'Pestisida serbaguna dari super admin',
            'created_by' => $superAdmin->id,
        ]);

        // Buat produk oleh owner 1 (hanya terlihat oleh admin gudang owner 1)
        Product::create([
            'name' => 'Benih Padi Lokal',
            'category_id' => $categoryBenih->id,
            'price' => 45000,
            'hpp' => 35000,
            'stock' => 100,
            'unit' => 'kg',
            'description' => 'Benih padi varietas lokal',
            'created_by' => $owner1->id,
        ]);

        // Buat produk oleh owner 2 (hanya terlihat oleh admin gudang owner 2)
        Product::create([
            'name' => 'Pupuk Organik Khusus',
            'category_id' => $categoryPupuk->id,
            'price' => 65000,
            'hpp' => 55000,
            'stock' => 75,
            'unit' => 'kg',
            'description' => 'Pupuk organik khusus toko B',
            'created_by' => $owner2->id,
        ]);

        $this->command->info('Test data untuk sinkronisasi produk berhasil dibuat!');
        $this->command->info('');
        $this->command->info('Login credentials:');
        $this->command->info('Super Admin: <EMAIL> / password');
        $this->command->info('Owner Toko A: <EMAIL> / password');
        $this->command->info('Owner Toko B: <EMAIL> / password');
        $this->command->info('Admin Gudang Toko A: <EMAIL> / password');
        $this->command->info('Admin Gudang Toko B: <EMAIL> / password');
        $this->command->info('');
        $this->command->info('Produk yang dibuat Super Admin akan terlihat oleh SEMUA admin gudang');
        $this->command->info('Produk yang dibuat Owner hanya terlihat oleh admin gudang owner tersebut');
    }
}
