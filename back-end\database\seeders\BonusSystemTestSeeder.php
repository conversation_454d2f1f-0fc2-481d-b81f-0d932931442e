<?php

namespace Database\Seeders;

use App\Models\User;
use App\Models\Category;
use App\Models\Product;
use App\Models\Mitra;
use App\Models\Bonus;
use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\Hash;

class BonusSystemTestSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Buat Owner
        $owner = User::create([
            'name' => 'Owner Bonus Test',
            'email' => '<EMAIL>',
            'phone' => '628111000001',
            'role' => 'owner',
            'password' => Hash::make('password'),
        ]);

        // Buat Sales
        $sales = User::create([
            'name' => 'Sales Bonus Test',
            'email' => '<EMAIL>',
            'phone' => '628222000001',
            'role' => 'sales',
            'owner_id' => $owner->id,
            'password' => Hash::make('password'),
        ]);

        // Buat Mitra
        $mitra1 = Mitra::create([
            'name' => 'Toko <PERSON>',
            'phone' => '628333000001',
            'address' => 'Jl. Pertanian No. 123',
            'credit_limit' => 5000000,
            'current_debt' => 0,
            'status' => 'active',
            'owner_id' => $owner->id,
            'created_by' => $owner->id,
        ]);

        $mitra2 = Mitra::create([
            'name' => 'CV Agro Sejahtera',
            'phone' => '628333000002',
            'address' => 'Jl. Raya Desa No. 456',
            'credit_limit' => 3000000,
            'current_debt' => 0,
            'status' => 'active',
            'owner_id' => $owner->id,
            'created_by' => $owner->id,
        ]);

        // Buat Kategori
        $categoryPupuk = Category::create([
            'name' => 'Pupuk',
            'description' => 'Kategori untuk produk pupuk',
            'created_by' => $owner->id,
        ]);

        $categoryBenih = Category::create([
            'name' => 'Benih',
            'description' => 'Kategori untuk produk benih',
            'created_by' => $owner->id,
        ]);

        $categoryPestisida = Category::create([
            'name' => 'Pestisida',
            'description' => 'Kategori untuk produk pestisida',
            'created_by' => $owner->id,
        ]);

        // Buat Produk
        $pupukNPK = Product::create([
            'name' => 'Pupuk NPK 16-16-16',
            'category_id' => $categoryPupuk->id,
            'price' => 125000,
            'hpp' => 100000,
            'stock' => 200,
            'unit' => 'kg',
            'description' => 'Pupuk NPK berkualitas tinggi',
            'created_by' => $owner->id,
        ]);

        $pupukUrea = Product::create([
            'name' => 'Pupuk Urea',
            'category_id' => $categoryPupuk->id,
            'price' => 85000,
            'hpp' => 70000,
            'stock' => 150,
            'unit' => 'kg',
            'description' => 'Pupuk Urea untuk tanaman',
            'created_by' => $owner->id,
        ]);

        $benihPadi = Product::create([
            'name' => 'Benih Padi IR64',
            'category_id' => $categoryBenih->id,
            'price' => 45000,
            'hpp' => 35000,
            'stock' => 100,
            'unit' => 'kg',
            'description' => 'Benih padi varietas IR64',
            'created_by' => $owner->id,
        ]);

        $pestisida = Product::create([
            'name' => 'Pestisida Organik',
            'category_id' => $categoryPestisida->id,
            'price' => 150000,
            'hpp' => 120000,
            'stock' => 75,
            'unit' => 'liter',
            'description' => 'Pestisida organik ramah lingkungan',
            'created_by' => $owner->id,
        ]);

        // Buat Bonus dengan berbagai jenis

        // 1. Bonus Periode untuk kategori Pupuk
        Bonus::create([
            'name' => 'Promo Pupuk Ramadan',
            'category_id' => $categoryPupuk->id,
            'minimum_quantity' => 20,
            'bonus_description' => 'Gratis pupuk organik 5kg untuk pembelian pupuk minimal 20kg',
            'type' => 'periode',
            'start_date' => now()->subDays(5),
            'end_date' => now()->addDays(25),
            'status' => 'active',
            'created_by' => $owner->id,
        ]);

        // 2. Bonus Quota untuk produk spesifik
        Bonus::create([
            'name' => 'Bonus Beli Benih Padi',
            'category_id' => $categoryBenih->id,
            'product_id' => $benihPadi->id,
            'minimum_quantity' => 10,
            'bonus_description' => 'Gratis pupuk NPK 2kg untuk pembelian benih padi minimal 10kg',
            'type' => 'quota',
            'quota_total' => 50,
            'quota_used' => 0,
            'status' => 'active',
            'created_by' => $owner->id,
        ]);

        // 3. Bonus Periode untuk kategori Pestisida
        Bonus::create([
            'name' => 'Promo Pestisida Organik',
            'category_id' => $categoryPestisida->id,
            'minimum_quantity' => 5,
            'bonus_description' => 'Gratis sarung tangan dan masker untuk pembelian pestisida minimal 5 liter',
            'type' => 'periode',
            'start_date' => now()->addDays(1),
            'end_date' => now()->addDays(15),
            'status' => 'active',
            'created_by' => $owner->id,
        ]);

        // 4. Bonus Quota untuk kategori Pupuk (produk spesifik)
        Bonus::create([
            'name' => 'Bonus Pupuk NPK Premium',
            'category_id' => $categoryPupuk->id,
            'product_id' => $pupukNPK->id,
            'minimum_quantity' => 15,
            'bonus_description' => 'Gratis alat ukur pH tanah untuk pembelian Pupuk NPK minimal 15kg',
            'type' => 'quota',
            'quota_total' => 25,
            'quota_used' => 0,
            'status' => 'active',
            'created_by' => $owner->id,
        ]);

        // 5. Bonus yang tidak aktif (untuk testing)
        Bonus::create([
            'name' => 'Bonus Tidak Aktif',
            'category_id' => $categoryPupuk->id,
            'minimum_quantity' => 10,
            'bonus_description' => 'Bonus yang sedang tidak aktif',
            'type' => 'quota',
            'quota_total' => 100,
            'quota_used' => 0,
            'status' => 'inactive',
            'created_by' => $owner->id,
        ]);

        // 6. Bonus yang sudah kedaluwarsa (untuk testing)
        Bonus::create([
            'name' => 'Bonus Kedaluwarsa',
            'category_id' => $categoryBenih->id,
            'minimum_quantity' => 5,
            'bonus_description' => 'Bonus yang sudah kedaluwarsa',
            'type' => 'periode',
            'start_date' => now()->subDays(30),
            'end_date' => now()->subDays(10),
            'status' => 'active',
            'created_by' => $owner->id,
        ]);

        $this->command->info('Test data untuk sistem bonus berhasil dibuat!');
        $this->command->info('');
        $this->command->info('Login credentials:');
        $this->command->info('Owner: <EMAIL> / password');
        $this->command->info('Sales: <EMAIL> / password');
        $this->command->info('');
        $this->command->info('Data yang tersedia:');
        $this->command->info('- 2 Mitra untuk testing transaksi');
        $this->command->info('- 3 Kategori produk (Pupuk, Benih, Pestisida)');
        $this->command->info('- 4 Produk dengan stok yang cukup');
        $this->command->info('- 6 Bonus dengan berbagai jenis dan status');
        $this->command->info('');
        $this->command->info('Bonus yang tersedia:');
        $this->command->info('1. Promo Pupuk Ramadan (Periode, Aktif)');
        $this->command->info('2. Bonus Beli Benih Padi (Quota 50, Aktif)');
        $this->command->info('3. Promo Pestisida Organik (Periode, Aktif)');
        $this->command->info('4. Bonus Pupuk NPK Premium (Quota 25, Aktif)');
        $this->command->info('5. Bonus Tidak Aktif (untuk testing)');
        $this->command->info('6. Bonus Kedaluwarsa (untuk testing)');
        $this->command->info('');
        $this->command->info('Cara testing:');
        $this->command->info('1. Login sebagai owner untuk mengelola bonus');
        $this->command->info('2. Login sebagai sales untuk membuat transaksi');
        $this->command->info('3. Buat transaksi dengan quantity yang memenuhi syarat bonus');
        $this->command->info('4. Cek apakah bonus otomatis diklaim');
    }
}
