# Sistem Reward untuk Owner

## Deskripsi
Sistem reward memungkinkan owner untuk membuat program reward berdasarkan target penjualan produk tertentu. Ketika sales melakukan transaksi, sistem akan otomatis menghitung progress mitra menuju target reward. Owner dapat memonitor leaderboard mitra dan melihat siapa yang sudah memenuhi syarat untuk menerima reward.

## Fitur Utama

### 1. Manajemen Reward oleh Owner
- **<PERSON><PERSON>ward**: Nama program reward (Paket Haji, Motor, dll)
- **Deskripsi**: Penjelasan detail tentang reward
- **Kategori Produk**: <PERSON>ward berlaku untuk kategori produk tertentu
- **Produk Spesifik**: Opsional, reward hanya untuk produk tertentu dalam kategori
- **Target Penjualan**: Jumlah minimum yang harus dibeli untuk mendapat reward
- **Periode Program**: <PERSON>gal mulai dan berakhir program reward
- **<PERSON><PERSON>**: Nilai reward dalam rupiah (opsional)
- **Syarat dan Ketentuan**: Detail aturan program reward

### 2. Tracking Progress Otomatis
- Sistem otomatis menghitung progress setiap transaksi
- Update real-time progress mitra menuju target
- History pembelian tersimpan untuk audit
- Estimasi waktu penyelesaian berdasarkan rata-rata pembelian
- Notifikasi otomatis saat target tercapai

### 3. Leaderboard dan Monitoring
- Ranking mitra berdasarkan progress reward
- Detail statistik per mitra (total transaksi, rata-rata per hari, dll)
- Progress visual dengan bar chart
- Filter dan pencarian mitra
- Export data untuk reporting

## Komponen Sistem

### 1. Database Schema

#### Tabel `rewards`
```sql
- id: Primary key
- name: Nama reward
- description: Deskripsi reward
- category_id: Foreign key ke categories
- product_id: Foreign key ke products (nullable)
- target_quantity: Target penjualan
- start_date: Tanggal mulai program
- end_date: Tanggal berakhir program
- status: Status reward (active/inactive/completed/expired)
- terms_conditions: Syarat dan ketentuan
- reward_image: Gambar reward (nullable)
- reward_value: Nilai reward dalam rupiah
- created_by: Foreign key ke users (owner)
```

#### Tabel `reward_progresses`
```sql
- id: Primary key
- reward_id: Foreign key ke rewards
- mitra_id: Foreign key ke mitras
- current_quantity: Quantity saat ini yang sudah dibeli
- target_quantity: Target quantity (copy dari reward)
- progress_percentage: Persentase progress (0-100)
- status: Status progress (in_progress/completed/expired)
- completed_at: Waktu selesai (nullable)
- purchase_history: History pembelian dalam JSON
- notes: Catatan tambahan
```

### 2. Model dan Relasi
- **Reward**: Model utama dengan relasi ke Category, Product, User
- **RewardProgress**: Model progress dengan relasi ke Reward, Mitra
- **Accessor dan Method**: Untuk kalkulasi progress dan statistik

### 3. Service Layer
- **RewardService**: Menangani logika bisnis reward
  - `checkAndUpdateRewardProgress()`: Update progress saat transaksi
  - `getRewardLeaderboard()`: Ambil ranking mitra
  - `getRewardStatsForOwner()`: Statistik reward untuk owner
  - `updateExpiredRewards()`: Update status reward kedaluwarsa

### 4. API Endpoints

#### Manajemen Reward (Owner Only)
```
GET    /api/rewards           - Daftar reward
POST   /api/rewards           - Buat reward baru
GET    /api/rewards/{id}      - Detail reward
PUT    /api/rewards/{id}      - Update reward
DELETE /api/rewards/{id}      - Hapus reward
GET    /api/rewards/{id}/leaderboard - Leaderboard reward
GET    /api/rewards-stats     - Statistik reward
GET    /api/mitra/{id}/reward-progress - Progress mitra
```

### 5. Frontend Components
- **RewardManagement.tsx**: Interface utama untuk owner
  - Form create/edit reward dengan validasi
  - Tabel daftar reward dengan progress visual
  - Statistik dan monitoring
- **RewardLeaderboard.tsx**: Modal leaderboard mitra
  - Ranking mitra dengan progress detail
  - Statistik per mitra
  - Estimasi penyelesaian

## Contoh Skenario Penggunaan

### 1. Owner Membuat Reward
```json
{
  "name": "Paket Haji",
  "description": "Dapatkan paket haji gratis",
  "category_id": 1,
  "product_id": 5,
  "target_quantity": 1000,
  "start_date": "2024-08-01",
  "end_date": "2024-12-31",
  "reward_value": 35000000,
  "status": "active"
}
```

### 2. Sales Membuat Transaksi
- Sales buat transaksi Rinso 500 pcs untuk Mitra A
- Sistem otomatis cek reward yang berlaku
- Update progress Mitra A: 500/1000 (50%)
- Simpan history pembelian

### 3. Owner Melihat Leaderboard
- Klik icon mata pada reward
- Lihat ranking mitra:
  1. Mitra A: 500/1000 (50%) - Kurang 500 pcs
  2. Mitra B: 300/1000 (30%) - Kurang 700 pcs
  3. Mitra C: 100/1000 (10%) - Kurang 900 pcs

## Cara Kerja Sistem

### 1. Pembuatan Reward
1. Owner login dan akses menu Reward
2. Klik "Tambah Reward"
3. Isi form reward:
   - Nama dan deskripsi reward
   - Pilih kategori dan produk (opsional)
   - Set target penjualan
   - Tentukan periode program
   - Set nilai reward dan syarat ketentuan
4. Simpan reward dengan status aktif

### 2. Tracking Progress Otomatis
1. Sales membuat transaksi seperti biasa
2. Sistem cek reward yang berlaku untuk produk dalam transaksi
3. Jika ada reward yang cocok:
   - Update atau buat progress untuk mitra
   - Tambah quantity ke current_quantity
   - Hitung ulang progress_percentage
   - Simpan history pembelian
   - Cek apakah sudah mencapai target
4. Response transaksi include info reward progress

### 3. Monitoring oleh Owner
1. Lihat daftar reward dan statistiknya
2. Klik icon mata untuk melihat leaderboard
3. Monitor progress mitra secara real-time
4. Identifikasi mitra yang sudah memenuhi syarat
5. Berikan reward sesuai ketentuan

## Validasi dan Aturan Bisnis

### 1. Validasi Reward
- Nama reward wajib diisi
- Target quantity harus > 0
- Tanggal mulai < tanggal berakhir
- Tanggal mulai tidak boleh di masa lalu
- Kategori dan produk harus milik owner
- Produk harus dalam kategori yang dipilih

### 2. Aturan Progress
- Satu mitra hanya bisa punya satu progress per reward
- Progress otomatis dibuat saat transaksi pertama
- Quantity terakumulasi dari semua transaksi
- Status berubah menjadi 'completed' saat target tercapai
- Progress expired jika reward expired

### 3. Keamanan
- Hanya owner yang bisa CRUD reward
- Owner hanya bisa akses reward milik mereka
- Validasi owner scope untuk kategori dan produk
- Prevent manipulation progress manual

## Testing

### 1. Manual Testing
```bash
# Setup data test
php artisan db:seed --class=RewardSystemTestSeeder

# Login credentials
Owner: <EMAIL> / password
Sales: <EMAIL> / password
```

### 2. Data Test yang Tersedia
**Reward:**
- Paket Haji Rinso (Target: 1000 pcs)
- Motor Honda (Target: 2000 pcs)
- iPhone 15 Pro (Target: 50 pcs)
- Liburan Bali (Target: 5000 pcs)

**Progress Mitra:**
- Toko Berkah Jaya: 500/1000 pcs (50%)
- Warung Sari Rasa: 800/2000 pcs (40%)
- Toko Elektronik Maju: 50/50 pcs (100% - Selesai!)

### 3. Skenario Testing

#### Test Reward Creation:
1. Login sebagai owner
2. Buat reward baru dengan target 100 pcs
3. Verifikasi validasi form
4. Cek reward muncul di daftar

#### Test Progress Tracking:
1. Login sebagai sales
2. Buat transaksi dengan produk yang ada rewardnya
3. Verifikasi progress otomatis terupdate
4. Cek history pembelian tersimpan

#### Test Leaderboard:
1. Login sebagai owner
2. Klik icon mata pada reward
3. Verifikasi ranking mitra sesuai progress
4. Cek statistik dan estimasi penyelesaian

## Monitoring dan Maintenance

### 1. Scheduled Tasks
```php
// Update expired rewards (daily)
$schedule->call(function () {
    app(RewardService::class)->updateExpiredRewards();
})->daily();
```

### 2. Performance Optimization
- Index pada foreign keys dan status
- Eager loading untuk relasi
- Pagination untuk large datasets
- Cache untuk reward yang sering diakses

### 3. Logging dan Audit
- Log semua aktivitas reward
- Track perubahan progress
- Monitor completion rate
- Alert untuk anomali

## Pengembangan Lanjutan

### 1. Fitur Tambahan
- Reward bertingkat (multiple targets)
- Reward kombinasi produk
- Notifikasi WhatsApp untuk mitra
- Dashboard analytics untuk owner
- Auto-generate reward certificate

### 2. Integrasi
- Integrasi dengan sistem loyalty point
- Export data untuk reporting
- API untuk aplikasi mobile mitra
- Webhook untuk sistem eksternal
- Integration dengan payment gateway

### 3. Machine Learning
- Prediksi mitra yang akan menyelesaikan reward
- Rekomendasi target reward optimal
- Analisis pattern pembelian mitra
- Personalized reward suggestions

Sistem reward ini memberikan tools yang powerful bagi owner untuk meningkatkan penjualan melalui program insentif yang menarik, sambil memberikan transparansi penuh dalam tracking progress mitra.
