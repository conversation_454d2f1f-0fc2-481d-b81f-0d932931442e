<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('reward_progresses', function (Blueprint $table) {
            $table->id();
            $table->foreignId('reward_id')->constrained('rewards')->cascadeOnDelete(); // Reward yang diikuti
            $table->foreignId('mitra_id')->constrained('mitras')->cascadeOnDelete(); // Mitra yang mengikuti
            $table->integer('current_quantity')->default(0); // Quantity saat ini yang sudah dibeli
            $table->integer('target_quantity'); // Target quantity (copy dari reward untuk historical data)
            $table->decimal('progress_percentage', 5, 2)->default(0); // Persentase progress (0-100)
            $table->enum('status', ['in_progress', 'completed', 'expired'])->default('in_progress'); // Status progress
            $table->timestamp('completed_at')->nullable(); // Waktu selesai (jika sudah mencapai target)
            $table->json('purchase_history')->nullable(); // History pembelian dalam JSON
            $table->text('notes')->nullable(); // Catatan tambahan
            $table->timestamps();
            
            // Index untuk performa
            $table->index(['reward_id', 'mitra_id']);
            $table->index(['status', 'progress_percentage']);
            $table->index('current_quantity');
            
            // Unique constraint untuk mencegah duplikasi
            $table->unique(['reward_id', 'mitra_id']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('reward_progresses');
    }
};
