<?php

namespace Tests\Unit;

use Tests\TestCase;
use App\Models\Transaction;
use App\Models\TransactionLog;
use App\Models\User;
use App\Models\Category;
use App\Models\Product;
use App\Models\Mitra;
use App\Services\TransactionLogService;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Support\Facades\Hash;

class TransactionLogTest extends TestCase
{
    use RefreshDatabase;

    protected $transactionLogService;
    protected $owner;
    protected $adminGudang;
    protected $transaction;

    protected function setUp(): void
    {
        parent::setUp();

        $this->transactionLogService = new TransactionLogService();

        // Setup test data
        $this->owner = User::create([
            'name' => 'Owner Test',
            'email' => '<EMAIL>',
            'phone' => '628111111111',
            'role' => 'owner',
            'password' => Hash::make('password'),
        ]);

        $this->adminGudang = User::create([
            'name' => 'Admin Gudang Test',
            'email' => '<EMAIL>',
            'phone' => '628222222222',
            'role' => 'admin_gudang',
            'owner_id' => $this->owner->id,
            'password' => Hash::make('password'),
        ]);

        $sales = User::create([
            'name' => 'Sales Test',
            'email' => '<EMAIL>',
            'phone' => '628333333333',
            'role' => 'sales',
            'owner_id' => $this->owner->id,
            'password' => Hash::make('password'),
        ]);

        $mitra = Mitra::create([
            'name' => 'Mitra Test',
            'phone' => '628444444444',
            'address' => 'Test Address',
            'credit_limit' => 1000000,
            'current_debt' => 0,
            'status' => 'active',
            'owner_id' => $this->owner->id,
            'created_by' => $this->owner->id,
        ]);

        $this->transaction = Transaction::create([
            'mitra_id' => $mitra->id,
            'sales_id' => $sales->id,
            'subtotal' => 100000,
            'total' => 100000,
            'payment_method' => 'cash',
            'status' => 'pending_gudang',
        ]);
    }

    /** @test */
    public function it_logs_approve_gudang_action()
    {
        // Act
        $this->transactionLogService->logApproveGudang($this->transaction, $this->adminGudang);

        // Assert
        $this->assertDatabaseHas('transaction_logs', [
            'transaction_id' => $this->transaction->id,
            'user_id' => $this->adminGudang->id,
            'action' => 'approve_gudang',
            'previous_status' => 'pending_gudang',
            'new_status' => 'approved',
        ]);

        $log = TransactionLog::first();
        $this->assertEquals('Transaksi disetujui oleh admin gudang', $log->notes);
        $this->assertArrayHasKey('user_name', $log->metadata);
        $this->assertEquals($this->adminGudang->name, $log->metadata['user_name']);
    }

    /** @test */
    public function it_logs_reject_action()
    {
        $rejectionReason = 'Stok tidak mencukupi';

        // Act
        $this->transactionLogService->logReject($this->transaction, $this->adminGudang, $rejectionReason);

        // Assert
        $this->assertDatabaseHas('transaction_logs', [
            'transaction_id' => $this->transaction->id,
            'user_id' => $this->adminGudang->id,
            'action' => 'reject',
            'new_status' => 'rejected',
        ]);

        $log = TransactionLog::first();
        $this->assertStringContainsString($rejectionReason, $log->notes);
        $this->assertEquals($rejectionReason, $log->metadata['rejection_reason']);
    }

    /** @test */
    public function it_logs_ship_action()
    {
        $shippingDocument = 'RESI-123456';

        // Act
        $this->transactionLogService->logShip($this->transaction, $this->adminGudang, $shippingDocument);

        // Assert
        $this->assertDatabaseHas('transaction_logs', [
            'transaction_id' => $this->transaction->id,
            'user_id' => $this->adminGudang->id,
            'action' => 'ship',
            'previous_status' => 'approved',
            'new_status' => 'shipped',
        ]);

        $log = TransactionLog::first();
        $this->assertStringContainsString($shippingDocument, $log->notes);
        $this->assertEquals($shippingDocument, $log->metadata['shipping_document']);
    }

    /** @test */
    public function it_logs_deliver_action()
    {
        $deliveryDocument = 'DELIVERY-789';

        // Act
        $this->transactionLogService->logDeliver($this->transaction, $this->adminGudang, $deliveryDocument);

        // Assert
        $this->assertDatabaseHas('transaction_logs', [
            'transaction_id' => $this->transaction->id,
            'user_id' => $this->adminGudang->id,
            'action' => 'deliver',
            'previous_status' => 'shipped',
            'new_status' => 'delivered',
        ]);

        $log = TransactionLog::first();
        $this->assertStringContainsString($deliveryDocument, $log->notes);
        $this->assertEquals($deliveryDocument, $log->metadata['delivery_document']);
    }

    /** @test */
    public function it_gets_logs_for_transaction()
    {
        // Arrange: Create multiple logs
        $this->transactionLogService->logApproveGudang($this->transaction, $this->adminGudang);
        $this->transactionLogService->logShip($this->transaction, $this->adminGudang, 'RESI-123');

        // Act
        $logs = $this->transactionLogService->getLogsForTransaction($this->transaction->id);

        // Assert
        $this->assertCount(2, $logs);
        $this->assertEquals('ship', $logs->first()->action); // Latest first
        $this->assertEquals('approve_gudang', $logs->last()->action);
    }

    /** @test */
    public function it_gets_logs_for_owner()
    {
        // Arrange: Create logs
        $this->transactionLogService->logApproveGudang($this->transaction, $this->adminGudang);

        // Act
        $logs = $this->transactionLogService->getLogsForOwner($this->owner->id);

        // Assert
        $this->assertCount(1, $logs);
        $this->assertEquals($this->transaction->id, $logs->first()->transaction_id);
    }

    /** @test */
    public function it_gets_activity_stats()
    {
        // Arrange: Create various logs
        $this->transactionLogService->logApproveGudang($this->transaction, $this->adminGudang);
        $this->transactionLogService->logShip($this->transaction, $this->adminGudang, 'RESI-123');

        // Act
        $stats = $this->transactionLogService->getActivityStats($this->owner->id, 30);

        // Assert
        $this->assertEquals(1, $stats['approve_gudang']);
        $this->assertEquals(1, $stats['ship']);
        $this->assertEquals(0, $stats['reject']);
        $this->assertEquals(0, $stats['deliver']);
        $this->assertEquals(2, $stats['total']);
    }

    /** @test */
    public function owner_can_access_transaction_logs_api()
    {
        // Arrange: Create a log
        $this->transactionLogService->logApproveGudang($this->transaction, $this->adminGudang);

        // Act
        $response = $this->actingAs($this->owner, 'sanctum')
            ->getJson('/api/transaction-logs');

        // Assert
        $response->assertStatus(200);
        $response->assertJsonStructure([
            'data' => [
                '*' => [
                    'id',
                    'transaction_id',
                    'action',
                    'new_status',
                    'notes',
                    'created_at',
                    'user' => ['id', 'name', 'role'],
                    'transaction' => ['id', 'total']
                ]
            ]
        ]);
    }

    /** @test */
    public function admin_gudang_cannot_access_transaction_logs_api()
    {
        // Act
        $response = $this->actingAs($this->adminGudang, 'sanctum')
            ->getJson('/api/transaction-logs');

        // Assert
        $response->assertStatus(403);
    }

    /** @test */
    public function owner_can_get_activity_stats()
    {
        // Arrange: Create logs
        $this->transactionLogService->logApproveGudang($this->transaction, $this->adminGudang);

        // Act
        $response = $this->actingAs($this->owner, 'sanctum')
            ->getJson('/api/transaction-logs/stats');

        // Assert
        $response->assertStatus(200);
        $response->assertJsonStructure([
            'stats' => [
                'approve_gudang',
                'reject',
                'ship',
                'deliver',
                'total',
                'period_days'
            ],
            'most_active_admins'
        ]);
    }
}
