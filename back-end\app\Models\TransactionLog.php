<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class TransactionLog extends Model
{
    use HasFactory;

    protected $fillable = [
        'transaction_id',
        'user_id',
        'action',
        'previous_status',
        'new_status',
        'notes',
        'metadata'
    ];

    protected $casts = [
        'metadata' => 'array',
        'created_at' => 'datetime',
        'updated_at' => 'datetime',
    ];

    /**
     * Relasi ke Transaction
     */
    public function transaction()
    {
        return $this->belongsTo(Transaction::class);
    }

    /**
     * Relasi ke User (admin gudang yang melakukan aksi)
     */
    public function user()
    {
        return $this->belongsTo(User::class);
    }

    /**
     * Scope untuk filter berdasarkan transaction
     */
    public function scopeForTransaction($query, $transactionId)
    {
        return $query->where('transaction_id', $transactionId);
    }

    /**
     * Scope untuk filter berdasarkan user
     */
    public function scopeByUser($query, $userId)
    {
        return $query->where('user_id', $userId);
    }

    /**
     * Scope untuk filter berdasarkan action
     */
    public function scopeByAction($query, $action)
    {
        return $query->where('action', $action);
    }

    /**
     * Scope untuk filter berdasarkan owner scope
     * (untuk owner melihat log transaksi milik mereka)
     */
    public function scopeByOwnerScope($query, $ownerId)
    {
        return $query->whereHas('transaction', function ($q) use ($ownerId) {
            $q->whereHas('sales', function ($salesQuery) use ($ownerId) {
                $salesQuery->where('owner_id', $ownerId)->orWhere('id', $ownerId);
            });
        });
    }

    /**
     * Accessor untuk mendapatkan nama action yang user-friendly
     */
    public function getActionNameAttribute()
    {
        $actionNames = [
            'approve_gudang' => 'Menyetujui Transaksi',
            'reject' => 'Menolak Transaksi',
            'ship' => 'Mengirim Barang',
            'deliver' => 'Menyelesaikan Pengiriman'
        ];

        return $actionNames[$this->action] ?? $this->action;
    }

    /**
     * Accessor untuk mendapatkan nama status yang user-friendly
     */
    public function getStatusNameAttribute()
    {
        $statusNames = [
            'pending_owner' => 'Menunggu Persetujuan Owner',
            'pending_gudang' => 'Menunggu Persetujuan Gudang',
            'approved' => 'Disetujui',
            'rejected' => 'Ditolak',
            'shipped' => 'Dikirim',
            'delivered' => 'Selesai'
        ];

        return $statusNames[$this->new_status] ?? $this->new_status;
    }
}
