<?php

namespace App\Http\Controllers;

use App\Models\Transaction;
use App\Models\Setting;
use Illuminate\Http\Request;
use Spipu\Html2Pdf\Html2Pdf;

class PdfInvoiceController extends Controller
{
    public function show(Request $request, Transaction $transaction)
    {
        $transaction->load(['items', 'mitra:id,name,phone,address', 'sales:id,name,owner_id']);

        // Resolve business settings based on owner (sales owner or self)
        $ownerId = optional($transaction->sales)->owner_id ?: optional($transaction->sales)->id;
        $setting = null;
        if ($ownerId) {
            $setting = Setting::where('owner_id', $ownerId)->first();
        }
        if (!$setting) {
            $setting = Setting::first();
        }

        // Convert logo URL to absolute path for PDF generation
        $logoPath = null;
        if ($setting && $setting->logo_url) {
            // Extract relative path from URL and convert to absolute path
            if (strpos($setting->logo_url, '/storage/') !== false) {
                $relativePath = substr($setting->logo_url, strpos($setting->logo_url, '/storage/') + 9);
                $absolutePath = storage_path('app/public/' . $relativePath);
                if (file_exists($absolutePath)) {
                    $logoPath = $absolutePath;
                }
            }
        }

        $data = [
            'tx' => $transaction,
            'biz' => $setting->business_name ?? 'Faktur Penjualan',
            'addr' => $setting->address ?? '',
            'logo' => $logoPath,
            'now' => now(),
        ];

        $html = view('pdf.invoice', $data)->render();

        // Html2Pdf doesn't have 'id' (Indonesian) locale; use 'en' to avoid unknown language code error
        $pdf = new Html2Pdf('L', 'A4', 'en');
        $pdf->setDefaultFont('Helvetica');
        $pdf->pdf->SetTitle('Invoice TXN-' . $transaction->id);
        $pdf->writeHTML($html);

        $content = $pdf->output('invoice-TXN-' . $transaction->id . '.pdf', 'S');
        return response($content, 200, [
            'Content-Type' => 'application/pdf',
            'Content-Disposition' => 'inline; filename="invoice-TXN-' . $transaction->id . '.pdf"',
        ]);
    }
}
