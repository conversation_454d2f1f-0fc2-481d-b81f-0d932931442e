import React, { useState } from 'react';
import { useAuth } from '../context/AuthContext';
import {
  Home,
  Users,
  Package,
  ShoppingCart,
  BarChart3,
  Settings,
  LogOut,
  Menu,
  X,
  Warehouse,
  UserCheck,
  CreditCard,
  FileText,
  TrendingUp,
  Gift,
  Trophy,
  Ruler
} from 'lucide-react';

interface LayoutProps {
  children: React.ReactNode;
}

const Layout: React.FC<LayoutProps> = ({ children }) => {
  const { user, logout } = useAuth();
  const [sidebarOpen, setSidebarOpen] = useState(false);

  const getMenuItems = () => {
    const baseItems = [
      { icon: Home, label: 'Dashboard', path: 'dashboard' }
    ];

    if (user?.role === 'superadmin') {
      return [
        ...baseItems,
        { icon: Users, label: 'Management User', path: 'users' },
        { icon: UserCheck, label: 'Management Mitra', path: 'mitras' },
        { icon: Package, label: 'Kategori Produk', path: 'categories' },
        { icon: Package, label: 'Management Produk', path: 'products' },
        { icon: Ruler, label: 'Master Satuan', path: 'units' },
        { icon: Warehouse, label: 'Management Stok', path: 'stock' },
        { icon: ShoppingCart, label: 'Transaksi', path: 'transactions' },
        { icon: BarChart3, label: 'Laporan', path: 'reports' }
      ];
    }

    if (user?.role === 'owner') {
      return [
        ...baseItems,
        { icon: TrendingUp, label: 'Monitoring', path: 'monitoring' },
        { icon: FileText, label: 'Approval Diskon', path: 'discount-approval' },
        { icon: Gift, label: 'Management Reward', path: 'rewards' },
        { icon: Trophy, label: 'Management Bonus', path: 'bonuses' },
        { icon: CreditCard, label: 'Piutang', path: 'receivables' },
        { icon: BarChart3, label: 'Laporan', path: 'reports' },
        { icon: Settings, label: 'Pengaturan', path: 'settings' }
      ];
    }

    if (user?.role === 'admin_gudang') {
      return [
        ...baseItems,
        { icon: Package, label: 'Kategori Produk', path: 'categories' },
        { icon: Package, label: 'Produk', path: 'products' },
        { icon: Warehouse, label: 'Stok', path: 'stock' },
        { icon: FileText, label: 'History Stok', path: 'stock-history' },
        { icon: ShoppingCart, label: 'Transaksi', path: 'transactions' }
      ];
    }

    if (user?.role === 'sales') {
      return [
        ...baseItems,
        { icon: UserCheck, label: 'Mitra', path: 'mitras' },
        { icon: CreditCard, label: 'Request Transaksi', path: 'new-transaction' },
        { icon: ShoppingCart, label: 'Transaksi Saya', path: 'my-transactions' },
        { icon: CreditCard, label: 'Piutang', path: 'receivables' }
      ];
    }

    return baseItems;
  };

  const menuItems = getMenuItems();

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Mobile sidebar */}
      <div className={`fixed inset-0 z-50 lg:hidden ${sidebarOpen ? 'block' : 'hidden'}`}>
        <div className="fixed inset-0 bg-gray-600 bg-opacity-75" onClick={() => setSidebarOpen(false)}></div>
        <div className="fixed top-0 left-0 bottom-0 flex flex-col w-5/6 max-w-sm bg-white shadow-xl">
          <div className="flex items-center justify-between h-16 px-6 border-b border-gray-200">
            <div className="flex items-center"><img src="/logo.png" alt="Logo" className="h-14 w-auto" /></div>
            <button onClick={() => setSidebarOpen(false)} className="text-gray-500 hover:text-gray-700">
              <X size={24} />
            </button>
          </div>
          <nav className="flex-1 px-4 py-6 space-y-2 overflow-y-auto">
            {menuItems.map((item) => (
              <a
                key={item.path}
                href={`#${item.path}`}
                className="flex items-center px-4 py-3 text-gray-700 rounded-lg hover:bg-green-50 hover:text-green-600 transition-colors"
                onClick={() => setSidebarOpen(false)}
              >
                <item.icon size={20} className="mr-3" />
                {item.label}
              </a>
            ))}
          </nav>
          <div className="p-4 border-t border-gray-200">
            <button
              onClick={logout}
              className="flex items-center w-full px-4 py-3 text-gray-700 rounded-lg hover:bg-red-50 hover:text-red-600 transition-colors"
            >
              <LogOut size={20} className="mr-3" />
              Logout
            </button>
          </div>
        </div>
      </div>

      {/* Desktop sidebar */}
      <div className="hidden lg:fixed lg:inset-y-0 lg:left-0 lg:z-40 lg:flex lg:w-64 lg:flex-col">
        <div className="flex flex-col flex-grow bg-white border-r border-gray-200 shadow-sm">
          <div className="flex items-center h-16 flex-shrink-0 px-6 border-b border-gray-200">
            <div className="flex items-center"><img src="/logo.png" alt="Logo" className="h-14 w-auto" /></div>
          </div>
          <nav className="flex-1 px-4 py-6 space-y-2 overflow-y-auto">
            {menuItems.map((item) => (
              <a
                key={item.path}
                href={`#${item.path}`}
                className="flex items-center px-4 py-3 text-gray-700 rounded-lg hover:bg-green-50 hover:text-green-600 transition-colors"
              >
                <item.icon size={20} className="mr-3" />
                {item.label}
              </a>
            ))}
          </nav>
          <div className="p-4 border-t border-gray-200">
            <div className="flex items-center mb-4">
              <div className="w-8 h-8 bg-green-100 rounded-full flex items-center justify-center mr-3">
                <span className="text-sm font-medium text-green-600">
                  {user?.name.charAt(0)}
                </span>
              </div>
              <div>
                <p className="text-sm font-medium text-gray-900">{user?.name}</p>
                <p className="text-xs text-gray-500 capitalize">{user?.role.replace('_', ' ')}</p>
              </div>
            </div>
            <button
              onClick={logout}
              className="flex items-center w-full px-4 py-3 text-gray-700 rounded-lg hover:bg-red-50 hover:text-red-600 transition-colors"
            >
              <LogOut size={20} className="mr-3" />
              Logout
            </button>
          </div>
        </div>
      </div>

      {/* Main content */}
      <div className="flex flex-col flex-1 lg:ml-64">
        {/* Top bar */}
        <div className="flex items-center justify-between h-16 bg-white border-b border-gray-200 shadow-sm px-6">
          <button
            onClick={() => setSidebarOpen(true)}
            className="text-gray-500 hover:text-gray-700 lg:hidden"
          >
            <Menu size={24} />
          </button>
          <div className="flex items-center">
            <span className="text-sm text-gray-600">
              Welcome back, <span className="font-medium text-gray-900">{user?.name}</span>
            </span>
          </div>
        </div>

        {/* Page content */}
        <main className="flex-1 p-6">
          {children}
        </main>
      </div>
    </div>
  );
};

export default Layout;