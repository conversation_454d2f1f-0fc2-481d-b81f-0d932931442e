<?php

namespace Database\Seeders;

use App\Models\User;
use App\Models\Category;
use App\Models\Product;
use App\Models\Mitra;
use App\Models\Reward;
use App\Models\RewardProgress;
use App\Models\Unit;
use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\Hash;
use Carbon\Carbon;

class RewardSystemTestSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Buat Owner
        $owner = User::firstOrCreate(
            ['email' => '<EMAIL>'],
            [
                'name' => 'Owner Reward Test',
                'phone' => '628111000001',
                'role' => 'owner',
                'password' => Hash::make('password'),
            ]
        );

        // Buat Sales
        $sales = User::firstOrCreate(
            ['email' => '<EMAIL>'],
            [
                'name' => 'Sales Reward Test',
                'phone' => '628222000001',
                'role' => 'sales',
                'owner_id' => $owner->id,
                'password' => Hash::make('password'),
            ]
        );

        // Buat Unit (jika belum ada)
        $unitKg = Unit::firstOrCreate(
            ['name' => 'kg'],
            [
                'content' => '1 kilogram',
                'quantity' => 1,
                'base_unit' => 'kg',
                'description' => 'Satuan dasar kilogram',
                'status' => 'active',
                'created_by' => $owner->id,
            ]
        );

        $unitPcs = Unit::firstOrCreate(
            ['name' => 'pcs'],
            [
                'content' => '1 piece',
                'quantity' => 1,
                'base_unit' => 'pcs',
                'description' => 'Satuan dasar piece',
                'status' => 'active',
                'created_by' => $owner->id,
            ]
        );

        // Buat Kategori
        $categoryDetergent = Category::firstOrCreate(
            ['name' => 'Deterjen', 'created_by' => $owner->id],
            [
                'description' => 'Kategori untuk produk deterjen',
            ]
        );

        $categoryElektronik = Category::firstOrCreate(
            ['name' => 'Elektronik', 'created_by' => $owner->id],
            [
                'description' => 'Kategori untuk produk elektronik',
            ]
        );

        $categoryMakanan = Category::firstOrCreate(
            ['name' => 'Makanan', 'created_by' => $owner->id],
            [
                'description' => 'Kategori untuk produk makanan',
            ]
        );

        // Buat Produk
        $rinso = Product::firstOrCreate(
            ['name' => 'Rinso Anti Noda', 'created_by' => $owner->id],
            [
                'category_id' => $categoryDetergent->id,
                'price' => 15000,
                'hpp' => 12000,
                'stock' => 5000,
                'unit_id' => $unitPcs->id,
                'description' => 'Deterjen Rinso Anti Noda kemasan 800g',
            ]
        );

        $soklin = Product::firstOrCreate(
            ['name' => 'So Klin Pewangi', 'created_by' => $owner->id],
            [
                'category_id' => $categoryDetergent->id,
                'price' => 12000,
                'hpp' => 9500,
                'stock' => 3000,
                'unit_id' => $unitPcs->id,
                'description' => 'Deterjen So Klin dengan pewangi',
            ]
        );

        $smartphone = Product::firstOrCreate(
            ['name' => 'Smartphone Android', 'created_by' => $owner->id],
            [
                'category_id' => $categoryElektronik->id,
                'price' => 2500000,
                'hpp' => 2200000,
                'stock' => 50,
                'unit_id' => $unitPcs->id,
                'description' => 'Smartphone Android mid-range',
            ]
        );

        $indomie = Product::firstOrCreate(
            ['name' => 'Indomie Goreng', 'created_by' => $owner->id],
            [
                'category_id' => $categoryMakanan->id,
                'price' => 3500,
                'hpp' => 3000,
                'stock' => 10000,
                'unit_id' => $unitPcs->id,
                'description' => 'Mie instan Indomie rasa ayam bawang',
            ]
        );

        // Buat Mitra
        $mitra1 = Mitra::firstOrCreate(
            ['phone' => '628333000001'],
            [
                'name' => 'Toko Berkah Jaya',
                'address' => 'Jl. Raya Utama No. 123',
                'credit_limit' => 10000000,
                'current_debt' => 0,
                'status' => 'active',
                'owner_id' => $owner->id,
                'created_by' => $owner->id,
            ]
        );

        $mitra2 = Mitra::firstOrCreate(
            ['phone' => '628333000002'],
            [
                'name' => 'Warung Sari Rasa',
                'address' => 'Jl. Pasar Baru No. 456',
                'credit_limit' => 5000000,
                'current_debt' => 0,
                'status' => 'active',
                'owner_id' => $owner->id,
                'created_by' => $owner->id,
            ]
        );

        $mitra3 = Mitra::firstOrCreate(
            ['phone' => '628333000003'],
            [
                'name' => 'Toko Elektronik Maju',
                'address' => 'Jl. Teknologi No. 789',
                'credit_limit' => 15000000,
                'current_debt' => 0,
                'status' => 'active',
                'owner_id' => $owner->id,
                'created_by' => $owner->id,
            ]
        );

        // Buat Reward

        // 1. Reward Paket Haji untuk Rinso (contoh utama)
        $rewardHaji = Reward::firstOrCreate(
            ['name' => 'Paket Haji Rinso', 'created_by' => $owner->id],
            [
                'description' => 'Dapatkan paket haji gratis dengan membeli Rinso Anti Noda minimal 1000 pcs',
                'category_id' => $categoryDetergent->id,
                'product_id' => $rinso->id,
                'target_quantity' => 1000,
                'start_date' => Carbon::now()->subDays(10),
                'end_date' => Carbon::now()->addDays(50),
                'status' => 'active',
                'terms_conditions' => 'Berlaku untuk pembelian dalam periode program. Hadiah akan diberikan setelah target tercapai.',
                'reward_value' => 35000000,
            ]
        );

        // 2. Reward Motor untuk kategori Deterjen
        $rewardMotor = Reward::firstOrCreate(
            ['name' => 'Motor Matic Honda', 'created_by' => $owner->id],
            [
                'description' => 'Menangkan motor matic Honda Beat dengan membeli produk deterjen minimal 2000 pcs',
                'category_id' => $categoryDetergent->id,
                'product_id' => null, // Semua produk dalam kategori
                'target_quantity' => 2000,
                'start_date' => Carbon::now()->subDays(5),
                'end_date' => Carbon::now()->addDays(25),
                'status' => 'active',
                'terms_conditions' => 'Berlaku untuk semua produk deterjen. Kombinasi pembelian berbagai produk diperbolehkan.',
                'reward_value' => 18000000,
            ]
        );

        // 3. Reward Smartphone untuk produk elektronik
        $rewardPhone = Reward::firstOrCreate(
            ['name' => 'iPhone 15 Pro', 'created_by' => $owner->id],
            [
                'description' => 'Dapatkan iPhone 15 Pro dengan membeli smartphone minimal 50 unit',
                'category_id' => $categoryElektronik->id,
                'product_id' => $smartphone->id,
                'target_quantity' => 50,
                'start_date' => Carbon::now()->addDays(1),
                'end_date' => Carbon::now()->addDays(30),
                'status' => 'active',
                'terms_conditions' => 'Khusus untuk produk smartphone Android. Hadiah iPhone 15 Pro 256GB.',
                'reward_value' => 20000000,
            ]
        );

        // 4. Reward Liburan untuk Indomie
        $rewardLiburan = Reward::firstOrCreate(
            ['name' => 'Liburan ke Bali', 'created_by' => $owner->id],
            [
                'description' => 'Paket liburan ke Bali 4 hari 3 malam untuk 2 orang',
                'category_id' => $categoryMakanan->id,
                'product_id' => $indomie->id,
                'target_quantity' => 5000,
                'start_date' => Carbon::now()->subDays(15),
                'end_date' => Carbon::now()->addDays(15),
                'status' => 'active',
                'terms_conditions' => 'Paket sudah termasuk tiket pesawat, hotel, dan tour guide.',
                'reward_value' => 8000000,
            ]
        );

        // 5. Reward yang sudah expired (untuk testing)
        $rewardExpired = Reward::firstOrCreate(
            ['name' => 'Reward Kedaluwarsa', 'created_by' => $owner->id],
            [
                'description' => 'Reward yang sudah kedaluwarsa untuk testing',
                'category_id' => $categoryDetergent->id,
                'product_id' => null,
                'target_quantity' => 100,
                'start_date' => Carbon::now()->subDays(30),
                'end_date' => Carbon::now()->subDays(5),
                'status' => 'expired',
                'terms_conditions' => 'Reward ini sudah kedaluwarsa.',
                'reward_value' => 1000000,
            ]
        );

        // Buat Progress Reward (simulasi mitra yang sudah mulai)

        // Mitra 1 - Progress untuk Reward Haji (sudah 500 pcs)
        $progress1 = RewardProgress::firstOrCreate(
            ['reward_id' => $rewardHaji->id, 'mitra_id' => $mitra1->id],
            [
                'target_quantity' => $rewardHaji->target_quantity,
                'current_quantity' => 500,
                'progress_percentage' => 50.0,
                'status' => 'in_progress',
                'purchase_history' => [
                    [
                        'date' => Carbon::now()->subDays(8)->toDateString(),
                        'quantity' => 200,
                        'total_quantity' => 200,
                        'transaction_data' => ['transaction_id' => 1, 'sales_id' => $sales->id],
                        'created_at' => Carbon::now()->subDays(8)->toISOString()
                    ],
                    [
                        'date' => Carbon::now()->subDays(5)->toDateString(),
                        'quantity' => 150,
                        'total_quantity' => 350,
                        'transaction_data' => ['transaction_id' => 2, 'sales_id' => $sales->id],
                        'created_at' => Carbon::now()->subDays(5)->toISOString()
                    ],
                    [
                        'date' => Carbon::now()->subDays(2)->toDateString(),
                        'quantity' => 150,
                        'total_quantity' => 500,
                        'transaction_data' => ['transaction_id' => 3, 'sales_id' => $sales->id],
                        'created_at' => Carbon::now()->subDays(2)->toISOString()
                    ]
                ]
            ]
        );

        // Mitra 2 - Progress untuk Reward Motor (sudah 800 pcs)
        $progress2 = RewardProgress::firstOrCreate(
            ['reward_id' => $rewardMotor->id, 'mitra_id' => $mitra2->id],
            [
                'target_quantity' => $rewardMotor->target_quantity,
                'current_quantity' => 800,
                'progress_percentage' => 40.0,
                'status' => 'in_progress',
                'purchase_history' => [
                    [
                        'date' => Carbon::now()->subDays(4)->toDateString(),
                        'quantity' => 400,
                        'total_quantity' => 400,
                        'transaction_data' => ['transaction_id' => 4, 'sales_id' => $sales->id],
                        'created_at' => Carbon::now()->subDays(4)->toISOString()
                    ],
                    [
                        'date' => Carbon::now()->subDays(1)->toDateString(),
                        'quantity' => 400,
                        'total_quantity' => 800,
                        'transaction_data' => ['transaction_id' => 5, 'sales_id' => $sales->id],
                        'created_at' => Carbon::now()->subDays(1)->toISOString()
                    ]
                ]
            ]
        );

        // Mitra 3 - Progress untuk Reward Smartphone (sudah selesai!)
        $progress3 = RewardProgress::firstOrCreate(
            ['reward_id' => $rewardPhone->id, 'mitra_id' => $mitra3->id],
            [
                'target_quantity' => $rewardPhone->target_quantity,
                'current_quantity' => 50,
                'progress_percentage' => 100.0,
                'status' => 'completed',
                'completed_at' => Carbon::now()->subDays(1),
                'purchase_history' => [
                    [
                        'date' => Carbon::now()->subDays(3)->toDateString(),
                        'quantity' => 25,
                        'total_quantity' => 25,
                        'transaction_data' => ['transaction_id' => 6, 'sales_id' => $sales->id],
                        'created_at' => Carbon::now()->subDays(3)->toISOString()
                    ],
                    [
                        'date' => Carbon::now()->subDays(1)->toDateString(),
                        'quantity' => 25,
                        'total_quantity' => 50,
                        'transaction_data' => ['transaction_id' => 7, 'sales_id' => $sales->id],
                        'created_at' => Carbon::now()->subDays(1)->toISOString()
                    ]
                ]
            ]
        );

        $this->command->info('Test data untuk sistem reward berhasil dibuat!');
        $this->command->info('');
        $this->command->info('Login credentials:');
        $this->command->info('Owner: <EMAIL> / password');
        $this->command->info('Sales: <EMAIL> / password');
        $this->command->info('');
        $this->command->info('Reward yang tersedia:');
        $this->command->info('1. Paket Haji Rinso (Target: 1000 pcs) - Aktif');
        $this->command->info('2. Motor Matic Honda (Target: 2000 pcs) - Aktif');
        $this->command->info('3. iPhone 15 Pro (Target: 50 pcs) - Aktif');
        $this->command->info('4. Liburan ke Bali (Target: 5000 pcs) - Aktif');
        $this->command->info('5. Reward Kedaluwarsa (untuk testing) - Expired');
        $this->command->info('');
        $this->command->info('Progress Mitra:');
        $this->command->info('- Toko Berkah Jaya: 500/1000 pcs untuk Paket Haji (50%)');
        $this->command->info('- Warung Sari Rasa: 800/2000 pcs untuk Motor Honda (40%)');
        $this->command->info('- Toko Elektronik Maju: 50/50 pcs untuk iPhone (100% - SELESAI!)');
        $this->command->info('');
        $this->command->info('Cara testing:');
        $this->command->info('1. Login sebagai owner untuk melihat reward dan leaderboard');
        $this->command->info('2. Login sebagai sales untuk membuat transaksi');
        $this->command->info('3. Buat transaksi dengan produk yang memiliki reward');
        $this->command->info('4. Cek progress reward otomatis terupdate');
        $this->command->info('5. Klik icon mata untuk melihat leaderboard mitra');
    }
}
