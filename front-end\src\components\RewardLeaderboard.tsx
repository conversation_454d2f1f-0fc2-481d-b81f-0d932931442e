import React, { useState, useEffect } from 'react';
import { X, Trophy, Medal, Award, Target, Calendar, TrendingUp } from 'lucide-react';
import { apiFetch } from '../services/api';
import { safeNumber, safeToFixed, safeToLocaleString, safeDate } from '../utils/numberUtils';

interface Reward {
  id: number;
  name: string;
  target_quantity: number;
  category: string;
  product?: string;
  end_date: string;
  remaining_days: number;
}

interface LeaderboardEntry {
  rank: number;
  mitra: {
    id: number;
    name: string;
    phone: string;
    address: string;
  };
  progress: {
    current_quantity: number;
    target_quantity: number;
    remaining_quantity: number;
    progress_percentage: number;
    status: string;
    status_name: string;
    completed_at?: string;
  };
  statistics: {
    total_transactions: number;
    average_per_transaction: number;
    average_per_day: number;
    days_since_start: number;
    is_completed: boolean;
  };
  estimated_completion?: string;
}

interface RewardStatistics {
  total_participants: number;
  completed_participants: number;
  completion_rate: number;
  total_sold_quantity: number;
  overall_progress: number;
  remaining_days: number;
  is_valid: boolean;
  is_expired: boolean;
}

interface Props {
  reward: Reward;
  isOpen: boolean;
  onClose: () => void;
}

const RewardLeaderboard: React.FC<Props> = ({ reward, isOpen, onClose }) => {
  const [leaderboard, setLeaderboard] = useState<LeaderboardEntry[]>([]);
  const [statistics, setStatistics] = useState<RewardStatistics | null>(null);
  const [loading, setLoading] = useState(false);

  useEffect(() => {
    if (isOpen && reward) {
      fetchLeaderboard();
    }
  }, [isOpen, reward]);

  const fetchLeaderboard = async () => {
    setLoading(true);
    try {
      const data = await apiFetch(`/api/rewards/${reward.id}/leaderboard`);
      setLeaderboard(data.leaderboard || []);
      setStatistics(data.statistics || null);
    } catch (error) {
      console.error('Error fetching leaderboard:', error);
    } finally {
      setLoading(false);
    }
  };

  const getRankIcon = (rank: number) => {
    switch (rank) {
      case 1: return <Trophy className="h-6 w-6 text-yellow-500" />;
      case 2: return <Medal className="h-6 w-6 text-gray-400" />;
      case 3: return <Award className="h-6 w-6 text-orange-500" />;
      default: return <span className="text-lg font-bold text-gray-600">#{rank}</span>;
    }
  };

  const getProgressColor = (percentage: number) => {
    if (percentage >= 100) return 'bg-green-500';
    if (percentage >= 75) return 'bg-blue-500';
    if (percentage >= 50) return 'bg-yellow-500';
    return 'bg-gray-400';
  };

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('id-ID', {
      style: 'currency',
      currency: 'IDR',
      minimumFractionDigits: 0
    }).format(amount);
  };

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50">
      <div className="relative top-20 mx-auto p-5 border w-11/12 md:w-5/6 lg:w-4/5 shadow-lg rounded-md bg-white">
        <div className="flex justify-between items-center mb-6">
          <h3 className="text-xl font-bold text-gray-900">
            Leaderboard: {reward.name}
          </h3>
          <button
            onClick={onClose}
            className="text-gray-400 hover:text-gray-600"
          >
            <X className="h-6 w-6" />
          </button>
        </div>

        {/* Reward Info */}
        <div className="bg-gradient-to-r from-blue-50 to-purple-50 p-4 rounded-lg mb-6">
          <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
            <div className="flex items-center">
              <Target className="h-5 w-5 text-blue-600 mr-2" />
              <div>
                <p className="text-sm text-gray-600">Target</p>
                <p className="font-semibold">{safeToLocaleString(reward.target_quantity)} unit</p>
              </div>
            </div>
            <div className="flex items-center">
              <Calendar className="h-5 w-5 text-green-600 mr-2" />
              <div>
                <p className="text-sm text-gray-600">Berakhir</p>
                <p className="font-semibold">{safeDate(reward.end_date)}</p>
              </div>
            </div>
            <div className="flex items-center">
              <TrendingUp className="h-5 w-5 text-purple-600 mr-2" />
              <div>
                <p className="text-sm text-gray-600">Kategori</p>
                <p className="font-semibold">{reward.category}</p>
              </div>
            </div>
            <div className="flex items-center">
              <Trophy className="h-5 w-5 text-yellow-600 mr-2" />
              <div>
                <p className="text-sm text-gray-600">Sisa Hari</p>
                <p className="font-semibold">{reward.remaining_days} hari</p>
              </div>
            </div>
          </div>
        </div>

        {/* Statistics */}
        {statistics && (
          <div className="grid grid-cols-1 md:grid-cols-4 gap-4 mb-6">
            <div className="bg-white p-4 rounded-lg border">
              <p className="text-sm text-gray-600">Total Partisipan</p>
              <p className="text-2xl font-bold text-blue-600">{safeNumber(statistics.total_participants)}</p>
            </div>
            <div className="bg-white p-4 rounded-lg border">
              <p className="text-sm text-gray-600">Sudah Selesai</p>
              <p className="text-2xl font-bold text-green-600">{safeNumber(statistics.completed_participants)}</p>
            </div>
            <div className="bg-white p-4 rounded-lg border">
              <p className="text-sm text-gray-600">Tingkat Penyelesaian</p>
              <p className="text-2xl font-bold text-purple-600">{safeNumber(statistics.completion_rate)}%</p>
            </div>
            <div className="bg-white p-4 rounded-lg border">
              <p className="text-sm text-gray-600">Progress Keseluruhan</p>
              <p className="text-2xl font-bold text-orange-600">{safeToFixed(statistics.overall_progress, 1)}%</p>
            </div>
          </div>
        )}

        {/* Leaderboard */}
        <div className="bg-white rounded-lg border">
          <div className="px-6 py-4 border-b">
            <h4 className="text-lg font-semibold text-gray-900">Ranking Mitra</h4>
          </div>

          {loading ? (
            <div className="flex justify-center items-center py-8">
              <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500"></div>
            </div>
          ) : leaderboard.length === 0 ? (
            <div className="text-center py-8">
              <Trophy className="mx-auto h-12 w-12 text-gray-400" />
              <h3 className="mt-2 text-sm font-medium text-gray-900">Belum ada partisipan</h3>
              <p className="mt-1 text-sm text-gray-500">Belum ada mitra yang berpartisipasi dalam reward ini.</p>
            </div>
          ) : (
            <div className="overflow-x-auto">
              <table className="min-w-full divide-y divide-gray-200">
                <thead className="bg-gray-50">
                  <tr>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Rank
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Mitra
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Progress
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Statistik
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Status
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Estimasi Selesai
                    </th>
                  </tr>
                </thead>
                <tbody className="bg-white divide-y divide-gray-200">
                  {leaderboard.map((entry) => (
                    <tr key={entry.mitra.id} className={entry.rank <= 3 ? 'bg-yellow-50' : ''}>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div className="flex items-center">
                          {getRankIcon(entry.rank)}
                        </div>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div>
                          <div className="text-sm font-medium text-gray-900">{entry.mitra.name}</div>
                          <div className="text-sm text-gray-500">{entry.mitra.phone}</div>
                          <div className="text-xs text-gray-400">{entry.mitra.address}</div>
                        </div>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div>
                          <div className="text-sm text-gray-900">
                            {safeToLocaleString(entry.progress.current_quantity)} / {safeToLocaleString(entry.progress.target_quantity)} unit
                          </div>
                          <div className="w-full bg-gray-200 rounded-full h-2 mt-1">
                            <div
                              className={`h-2 rounded-full ${getProgressColor(safeNumber(entry.progress.progress_percentage))}`}
                              style={{ width: `${Math.min(100, safeNumber(entry.progress.progress_percentage))}%` }}
                            ></div>
                          </div>
                          <div className="text-xs text-gray-500 mt-1">
                            {safeToFixed(entry.progress.progress_percentage, 1)}%
                            {safeNumber(entry.progress.remaining_quantity) > 0 && (
                              <span> (kurang {safeToLocaleString(entry.progress.remaining_quantity)})</span>
                            )}
                          </div>
                        </div>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div className="text-xs text-gray-600">
                          <div>{safeNumber(entry.statistics.total_transactions)} transaksi</div>
                          <div>Rata-rata: {safeToFixed(entry.statistics.average_per_transaction, 1)}/transaksi</div>
                          <div>Per hari: {safeToFixed(entry.statistics.average_per_day, 1)} unit</div>
                        </div>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${entry.progress.status === 'completed'
                          ? 'bg-green-100 text-green-800'
                          : 'bg-yellow-100 text-yellow-800'
                          }`}>
                          {entry.progress.status_name}
                        </span>
                        {entry.progress.completed_at && (
                          <div className="text-xs text-gray-500 mt-1">
                            {entry.progress.completed_at}
                          </div>
                        )}
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div className="text-sm text-gray-900">
                          {entry.progress.status === 'completed'
                            ? 'Selesai!'
                            : entry.estimated_completion || 'Tidak dapat diprediksi'
                          }
                        </div>
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          )}
        </div>

        <div className="flex justify-end mt-6">
          <button
            onClick={onClose}
            className="px-4 py-2 bg-gray-600 text-white rounded-md hover:bg-gray-700"
          >
            Tutup
          </button>
        </div>
      </div>
    </div>
  );
};

export default RewardLeaderboard;
