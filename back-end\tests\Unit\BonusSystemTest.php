<?php

namespace Tests\Unit;

use Tests\TestCase;
use App\Models\Bonus;
use App\Models\BonusClaim;
use App\Models\User;
use App\Models\Category;
use App\Models\Product;
use App\Models\Mitra;
use App\Models\Transaction;
use App\Models\TransactionItem;
use App\Services\BonusService;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Support\Facades\Hash;

class BonusSystemTest extends TestCase
{
    use RefreshDatabase;

    protected $bonusService;
    protected $owner;
    protected $sales;
    protected $mitra;
    protected $category;
    protected $product;

    protected function setUp(): void
    {
        parent::setUp();
        
        $this->bonusService = new BonusService();
        
        // Setup test data
        $this->owner = User::create([
            'name' => 'Owner Test',
            'email' => '<EMAIL>',
            'phone' => '628111111111',
            'role' => 'owner',
            'password' => Hash::make('password'),
        ]);

        $this->sales = User::create([
            'name' => 'Sales Test',
            'email' => '<EMAIL>',
            'phone' => '628222222222',
            'role' => 'sales',
            'owner_id' => $this->owner->id,
            'password' => Hash::make('password'),
        ]);

        $this->mitra = Mitra::create([
            'name' => 'Mitra Test',
            'phone' => '628333333333',
            'address' => 'Test Address',
            'credit_limit' => 1000000,
            'current_debt' => 0,
            'status' => 'active',
            'owner_id' => $this->owner->id,
            'created_by' => $this->owner->id,
        ]);

        $this->category = Category::create([
            'name' => 'Test Category',
            'description' => 'Test category description',
            'created_by' => $this->owner->id,
        ]);

        $this->product = Product::create([
            'name' => 'Test Product',
            'category_id' => $this->category->id,
            'price' => 50000,
            'hpp' => 40000,
            'stock' => 100,
            'unit' => 'kg',
            'description' => 'Test product description',
            'created_by' => $this->owner->id,
        ]);
    }

    /** @test */
    public function it_creates_bonus_with_periode_type()
    {
        $bonus = Bonus::create([
            'name' => 'Bonus Periode Test',
            'category_id' => $this->category->id,
            'minimum_quantity' => 10,
            'bonus_description' => 'Gratis pupuk 5kg',
            'type' => 'periode',
            'start_date' => now()->addDay(),
            'end_date' => now()->addDays(30),
            'status' => 'active',
            'created_by' => $this->owner->id,
        ]);

        $this->assertDatabaseHas('bonuses', [
            'name' => 'Bonus Periode Test',
            'type' => 'periode',
            'status' => 'active'
        ]);

        $this->assertEquals('periode', $bonus->type);
        $this->assertNotNull($bonus->start_date);
        $this->assertNotNull($bonus->end_date);
        $this->assertNull($bonus->quota_total);
    }

    /** @test */
    public function it_creates_bonus_with_quota_type()
    {
        $bonus = Bonus::create([
            'name' => 'Bonus Quota Test',
            'category_id' => $this->category->id,
            'minimum_quantity' => 5,
            'bonus_description' => 'Gratis benih 1kg',
            'type' => 'quota',
            'quota_total' => 100,
            'quota_used' => 0,
            'status' => 'active',
            'created_by' => $this->owner->id,
        ]);

        $this->assertDatabaseHas('bonuses', [
            'name' => 'Bonus Quota Test',
            'type' => 'quota',
            'quota_total' => 100,
            'quota_used' => 0
        ]);

        $this->assertEquals('quota', $bonus->type);
        $this->assertEquals(100, $bonus->quota_total);
        $this->assertEquals(0, $bonus->quota_used);
        $this->assertNull($bonus->start_date);
        $this->assertNull($bonus->end_date);
    }

    /** @test */
    public function it_checks_bonus_validity_for_periode_type()
    {
        // Bonus yang masih berlaku
        $validBonus = Bonus::create([
            'name' => 'Valid Bonus',
            'category_id' => $this->category->id,
            'minimum_quantity' => 10,
            'bonus_description' => 'Test bonus',
            'type' => 'periode',
            'start_date' => now()->subDay(),
            'end_date' => now()->addDay(),
            'status' => 'active',
            'created_by' => $this->owner->id,
        ]);

        // Bonus yang sudah kedaluwarsa
        $expiredBonus = Bonus::create([
            'name' => 'Expired Bonus',
            'category_id' => $this->category->id,
            'minimum_quantity' => 10,
            'bonus_description' => 'Test bonus',
            'type' => 'periode',
            'start_date' => now()->subDays(10),
            'end_date' => now()->subDay(),
            'status' => 'active',
            'created_by' => $this->owner->id,
        ]);

        $this->assertTrue($validBonus->isValid());
        $this->assertFalse($expiredBonus->isValid());
    }

    /** @test */
    public function it_checks_bonus_validity_for_quota_type()
    {
        // Bonus dengan quota tersisa
        $validBonus = Bonus::create([
            'name' => 'Valid Quota Bonus',
            'category_id' => $this->category->id,
            'minimum_quantity' => 10,
            'bonus_description' => 'Test bonus',
            'type' => 'quota',
            'quota_total' => 100,
            'quota_used' => 50,
            'status' => 'active',
            'created_by' => $this->owner->id,
        ]);

        // Bonus dengan quota habis
        $exhaustedBonus = Bonus::create([
            'name' => 'Exhausted Quota Bonus',
            'category_id' => $this->category->id,
            'minimum_quantity' => 10,
            'bonus_description' => 'Test bonus',
            'type' => 'quota',
            'quota_total' => 100,
            'quota_used' => 100,
            'status' => 'active',
            'created_by' => $this->owner->id,
        ]);

        $this->assertTrue($validBonus->isValid());
        $this->assertFalse($exhaustedBonus->isValid());
    }

    /** @test */
    public function it_checks_transaction_eligibility_for_category_bonus()
    {
        $bonus = Bonus::create([
            'name' => 'Category Bonus',
            'category_id' => $this->category->id,
            'minimum_quantity' => 10,
            'bonus_description' => 'Test bonus',
            'type' => 'quota',
            'quota_total' => 100,
            'quota_used' => 0,
            'status' => 'active',
            'created_by' => $this->owner->id,
        ]);

        $transaction = Transaction::create([
            'mitra_id' => $this->mitra->id,
            'sales_id' => $this->sales->id,
            'subtotal' => 500000,
            'total' => 500000,
            'payment_method' => 'cash',
            'status' => 'pending_gudang',
        ]);

        // Item yang memenuhi syarat (quantity >= minimum)
        $eligibleItem = TransactionItem::create([
            'transaction_id' => $transaction->id,
            'product_id' => $this->product->id,
            'product_name' => $this->product->name,
            'price' => $this->product->price,
            'quantity' => 15, // >= 10
            'total' => 750000,
        ]);

        $transaction->load(['items.product']);
        $this->assertTrue($bonus->isEligibleForTransaction($transaction->items));

        // Item yang tidak memenuhi syarat (quantity < minimum)
        $eligibleItem->update(['quantity' => 5]); // < 10
        $transaction->refresh();
        $transaction->load(['items.product']);
        $this->assertFalse($bonus->isEligibleForTransaction($transaction->items));
    }

    /** @test */
    public function it_checks_transaction_eligibility_for_product_bonus()
    {
        $bonus = Bonus::create([
            'name' => 'Product Bonus',
            'category_id' => $this->category->id,
            'product_id' => $this->product->id,
            'minimum_quantity' => 10,
            'bonus_description' => 'Test bonus',
            'type' => 'quota',
            'quota_total' => 100,
            'quota_used' => 0,
            'status' => 'active',
            'created_by' => $this->owner->id,
        ]);

        $transaction = Transaction::create([
            'mitra_id' => $this->mitra->id,
            'sales_id' => $this->sales->id,
            'subtotal' => 500000,
            'total' => 500000,
            'payment_method' => 'cash',
            'status' => 'pending_gudang',
        ]);

        TransactionItem::create([
            'transaction_id' => $transaction->id,
            'product_id' => $this->product->id,
            'product_name' => $this->product->name,
            'price' => $this->product->price,
            'quantity' => 15,
            'total' => 750000,
        ]);

        $transaction->load(['items.product']);
        $this->assertTrue($bonus->isEligibleForTransaction($transaction->items));
    }

    /** @test */
    public function it_processes_bonus_claim_successfully()
    {
        $bonus = Bonus::create([
            'name' => 'Test Bonus',
            'category_id' => $this->category->id,
            'minimum_quantity' => 10,
            'bonus_description' => 'Gratis pupuk 5kg',
            'type' => 'quota',
            'quota_total' => 100,
            'quota_used' => 0,
            'status' => 'active',
            'created_by' => $this->owner->id,
        ]);

        $transaction = Transaction::create([
            'mitra_id' => $this->mitra->id,
            'sales_id' => $this->sales->id,
            'subtotal' => 750000,
            'total' => 750000,
            'payment_method' => 'cash',
            'status' => 'pending_gudang',
        ]);

        TransactionItem::create([
            'transaction_id' => $transaction->id,
            'product_id' => $this->product->id,
            'product_name' => $this->product->name,
            'price' => $this->product->price,
            'quantity' => 15,
            'total' => 750000,
        ]);

        $claimedBonuses = $this->bonusService->checkAndClaimBonuses($transaction);

        $this->assertCount(1, $claimedBonuses);
        $this->assertEquals($bonus->id, $claimedBonuses[0]->id);

        // Cek apakah klaim tersimpan di database
        $this->assertDatabaseHas('bonus_claims', [
            'bonus_id' => $bonus->id,
            'transaction_id' => $transaction->id,
            'mitra_id' => $this->mitra->id,
            'quantity_purchased' => 15,
            'status' => 'pending'
        ]);

        // Cek apakah quota berkurang
        $bonus->refresh();
        $this->assertEquals(1, $bonus->quota_used);
    }

    /** @test */
    public function it_prevents_duplicate_bonus_claims()
    {
        $bonus = Bonus::create([
            'name' => 'Test Bonus',
            'category_id' => $this->category->id,
            'minimum_quantity' => 10,
            'bonus_description' => 'Gratis pupuk 5kg',
            'type' => 'quota',
            'quota_total' => 100,
            'quota_used' => 0,
            'status' => 'active',
            'created_by' => $this->owner->id,
        ]);

        $transaction = Transaction::create([
            'mitra_id' => $this->mitra->id,
            'sales_id' => $this->sales->id,
            'subtotal' => 750000,
            'total' => 750000,
            'payment_method' => 'cash',
            'status' => 'pending_gudang',
        ]);

        TransactionItem::create([
            'transaction_id' => $transaction->id,
            'product_id' => $this->product->id,
            'product_name' => $this->product->name,
            'price' => $this->product->price,
            'quantity' => 15,
            'total' => 750000,
        ]);

        // Klaim pertama
        $claimedBonuses1 = $this->bonusService->checkAndClaimBonuses($transaction);
        $this->assertCount(1, $claimedBonuses1);

        // Klaim kedua (seharusnya tidak ada)
        $claimedBonuses2 = $this->bonusService->checkAndClaimBonuses($transaction);
        $this->assertCount(0, $claimedBonuses2);

        // Pastikan hanya ada satu klaim di database
        $claimCount = BonusClaim::where('bonus_id', $bonus->id)
            ->where('transaction_id', $transaction->id)
            ->count();
        $this->assertEquals(1, $claimCount);
    }

    /** @test */
    public function owner_can_create_bonus_via_api()
    {
        $response = $this->actingAs($this->owner, 'sanctum')
            ->postJson('/api/bonuses', [
                'name' => 'API Test Bonus',
                'category_id' => $this->category->id,
                'minimum_quantity' => 10,
                'bonus_description' => 'Test bonus via API',
                'type' => 'periode',
                'start_date' => now()->addDay()->format('Y-m-d'),
                'end_date' => now()->addDays(30)->format('Y-m-d'),
                'status' => 'active'
            ]);

        $response->assertStatus(201);
        $response->assertJsonStructure([
            'id',
            'name',
            'category_id',
            'minimum_quantity',
            'bonus_description',
            'type',
            'start_date',
            'end_date',
            'status'
        ]);

        $this->assertDatabaseHas('bonuses', [
            'name' => 'API Test Bonus',
            'type' => 'periode'
        ]);
    }

    /** @test */
    public function owner_can_view_bonus_claims()
    {
        $bonus = Bonus::create([
            'name' => 'Test Bonus',
            'category_id' => $this->category->id,
            'minimum_quantity' => 10,
            'bonus_description' => 'Test bonus',
            'type' => 'quota',
            'quota_total' => 100,
            'quota_used' => 1,
            'status' => 'active',
            'created_by' => $this->owner->id,
        ]);

        $transaction = Transaction::create([
            'mitra_id' => $this->mitra->id,
            'sales_id' => $this->sales->id,
            'subtotal' => 750000,
            'total' => 750000,
            'payment_method' => 'cash',
            'status' => 'approved',
        ]);

        BonusClaim::create([
            'bonus_id' => $bonus->id,
            'transaction_id' => $transaction->id,
            'mitra_id' => $this->mitra->id,
            'quantity_purchased' => 15,
            'bonus_received' => 'Test bonus',
            'status' => 'pending'
        ]);

        $response = $this->actingAs($this->owner, 'sanctum')
            ->getJson('/api/bonus-claims');

        $response->assertStatus(200);
        $response->assertJsonStructure([
            'data' => [
                '*' => [
                    'id',
                    'bonus_id',
                    'transaction_id',
                    'mitra_id',
                    'quantity_purchased',
                    'bonus_received',
                    'status',
                    'bonus',
                    'transaction',
                    'mitra'
                ]
            ]
        ]);
    }
}
