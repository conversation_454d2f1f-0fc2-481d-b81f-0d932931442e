import React, { useState } from 'react';
// import { mockProducts, mockCategories } from '../services/mockData';
import { apiFetch } from '../services/api';
import { Product, Category, Unit } from '../types';
import { Plus, Edit, Trash2, Search, Package, X } from 'lucide-react';

// Simple Toast
const Toast: React.FC<{ type: 'success' | 'error'; message: string }> = ({ type, message }) => (
  <div className={`fixed top-6 right-6 z-50 px-4 py-3 rounded-lg shadow-lg text-sm ${type === 'success' ? 'bg-green-600 text-white' : 'bg-red-600 text-white'}`}>
    {message}
  </div>
);

const ProductManagement: React.FC = () => {
  const [products, setProducts] = useState<Product[]>([] as any);
  const [categories, setCategories] = useState<Category[]>([]);
  const [units, setUnits] = useState<Unit[]>([]);
  const [searchTerm, setSearchTerm] = useState('');
  const [toast, setToast] = useState<{ type: 'success' | 'error'; message: string } | null>(null);

  React.useEffect(() => {
    (async () => {
      try {
        const [catsRes, prodsRes, unitsRes] = await Promise.all([
          apiFetch('/api/categories'),
          apiFetch('/api/products'),
          apiFetch('/api/units/list'),
        ]);
        const catItems: Category[] = catsRes.data ?? catsRes;
        const prodItems: any[] = prodsRes.data ?? prodsRes;
        const unitItems: Unit[] = unitsRes.data ?? unitsRes;
        setCategories(catItems);
        setUnits(unitItems);
        setProducts(
          prodItems.map((p: any) => ({
            id: String(p.id),
            name: p.name,
            categoryId: String(p.category_id ?? p.categoryId),
            category: p.category?.name ?? p.category,
            price: Number(p.price),
            hpp: Number(p.hpp ?? 0),
            stock: Number(p.stock),
            unit: p.unit ?? '',
            description: p.description ?? '',
            createdBy: String(p.created_by ?? ''),
            createdAt: p.created_at ?? new Date().toISOString(),
          }))
        );
      } catch (e) {
        console.error(e);
      }
    })();
  }, []);

  const [selectedCategory, setSelectedCategory] = useState('');
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [editingProduct, setEditingProduct] = useState<Product | null>(null);
  const [formData, setFormData] = useState({
    name: '',
    categoryId: '',
    price: 0,
    hpp: 0,
    stock: 0,
    unit: '',
    description: ''
  });

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('id-ID', {
      style: 'currency',
      currency: 'IDR',
      minimumFractionDigits: 0
    }).format(amount);
  };

  const filteredProducts = products.filter(product => {
    const matchesSearch = product.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
      product.description.toLowerCase().includes(searchTerm.toLowerCase());
    const matchesCategory = !selectedCategory || product.categoryId === selectedCategory;
    return matchesSearch && matchesCategory;
  });

  const openModal = (product?: Product) => {
    if (product) {
      setEditingProduct(product);
      setFormData({
        name: product.name,
        categoryId: product.categoryId,
        price: product.price,
        hpp: (product as any).hpp ?? 0,
        stock: product.stock,
        unit: typeof product.unit === 'object' ? product.unit.id.toString() : (product.unit_id?.toString() || product.unit || ''),
        description: product.description
      });
    } else {
      setEditingProduct(null);
      setFormData({
        name: '',
        categoryId: '',
        price: 0,
        hpp: 0,
        stock: 0,
        unit: '',
        description: ''
      });
    }
    setIsModalOpen(true);
  };

  const closeModal = () => {
    setIsModalOpen(false);
    setEditingProduct(null);
    setFormData({
      name: '',
      categoryId: '',
      price: 0,
      hpp: 0,
      stock: 0,
      unit: '',
      description: ''
    });
  };

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();

    if (!formData.name.trim() || !formData.categoryId || formData.price <= 0) {
      setToast({ type: 'error', message: 'Semua field wajib diisi dengan benar' });
      setTimeout(() => setToast(null), 2500);
      return;
    }

    const category = categories.find(c => c.id === formData.categoryId);

    (async () => {
      try {
        if (editingProduct) {
          const updated = await apiFetch(`/api/products/${editingProduct.id}`, {
            method: 'PUT',
            body: JSON.stringify({
              name: formData.name,
              category_id: formData.categoryId,
              price: formData.price,
              hpp: (formData as any).hpp,
              stock: formData.stock,
              unit_id: formData.unit ? parseInt(formData.unit) : null,
              description: formData.description,
            }),
          });
          setProducts(prev => prev.map(p => p.id === editingProduct.id ? {
            id: String(updated.id),
            name: updated.name,
            categoryId: String(updated.category_id ?? updated.categoryId),
            category: updated.category?.name ?? category?.name ?? '',
            price: Number(updated.price),
            stock: Number(updated.stock),
            unit: updated.unit ?? '',
            description: updated.description ?? '',
            createdBy: String(updated.created_by ?? ''),
            createdAt: updated.created_at ?? new Date().toISOString(),
          } : p));
          setToast({ type: 'success', message: 'Produk berhasil diupdate!' });
          setTimeout(() => setToast(null), 2500);
        } else {
          const created = await apiFetch('/api/products', {
            method: 'POST',
            body: JSON.stringify({
              name: formData.name,
              category_id: formData.categoryId,
              price: formData.price,
              hpp: (formData as any).hpp,
              stock: formData.stock,
              unit_id: formData.unit ? parseInt(formData.unit) : null,
              description: formData.description,
            }),
          });
          setProducts(prev => [{
            id: String(created.id),
            name: created.name,
            categoryId: String(created.category_id ?? created.categoryId),
            category: created.category?.name ?? category?.name ?? '',
            price: Number(created.price),
            stock: Number(created.stock),
            unit: created.unit ?? '',
            description: created.description ?? '',
            createdBy: String(created.created_by ?? ''),
            createdAt: created.created_at ?? new Date().toISOString(),
          }, ...prev]);
          setToast({ type: 'success', message: 'Produk berhasil ditambahkan!' });
          setTimeout(() => setToast(null), 2500);
        }
        closeModal();
      } catch (e: any) {
        setToast({ type: 'error', message: e.message || 'Gagal menyimpan produk' });
        setTimeout(() => setToast(null), 2500);
      }
    })();
  };

  const handleDelete = (productId: string) => {
    if (!window.confirm('Apakah Anda yakin ingin menghapus produk ini?')) return;
    (async () => {
      try {
        await apiFetch(`/api/products/${productId}`, { method: 'DELETE' });
        setProducts(prev => prev.filter(product => product.id !== productId));
        setToast({ type: 'success', message: 'Produk berhasil dihapus!' });
        setTimeout(() => setToast(null), 2500);
      } catch (e: any) {
        setToast({ type: 'error', message: e.message || 'Gagal menghapus produk' });
        setTimeout(() => setToast(null), 2500);
      }
    })();
  };

  const getStockStatus = (stock: number) => {
    if (stock <= 10) return { color: 'text-red-600 bg-red-100', label: 'Stok Rendah' };
    if (stock <= 50) return { color: 'text-orange-600 bg-orange-100', label: 'Stok Sedang' };
    return { color: 'text-green-600 bg-green-100', label: 'Stok Cukup' };
  };

  const getUnitName = (product: Product) => {
    if (typeof product.unit === 'object' && product.unit) {
      return product.unit.name;
    }
    if (product.unit_id) {
      const unit = units.find(u => u.id === product.unit_id);
      return unit ? unit.name : '';
    }
    return product.unit_legacy || product.unit || '';
  };

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <h1 className="text-3xl font-bold text-gray-900">Management Produk</h1>
        {toast && <Toast type={toast.type} message={toast.message} />}

        <button
          onClick={() => openModal()}
          className="flex items-center px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 transition-colors"
        >
          <Plus size={20} className="mr-2" />
          Tambah Produk
        </button>
      </div>

      {/* Filters */}
      <div className="bg-white p-6 rounded-xl shadow-sm border border-gray-100">
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
          <div className="relative">
            <Search size={20} className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400" />
            <input
              type="text"
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-green-500 focus:border-green-500"
              placeholder="Cari produk..."
            />
          </div>
          <select
            value={selectedCategory}
            onChange={(e) => setSelectedCategory(e.target.value)}
            className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-green-500 focus:border-green-500"
          >
            <option value="">Semua Kategori</option>
            {categories.map((category) => (
              <option key={category.id} value={category.id}>
                {category.name}
              </option>
            ))}
          </select>
          <div className="text-sm text-gray-600 flex items-center">
            Total: {filteredProducts.length} produk
          </div>
        </div>
      </div>

      {/* Products Grid */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        {filteredProducts.map((product) => {
          const stockStatus = getStockStatus(product.stock);
          return (
            <div key={product.id} className="bg-white rounded-xl shadow-sm border border-gray-100 p-6 hover:shadow-md transition-shadow">
              <div className="flex items-start justify-between mb-4">
                <div className="flex-1 min-w-0">
                  <h3 className="text-lg font-semibold text-gray-900 mb-1 truncate" title={product.name}>{product.name}</h3>
                  <p className="text-sm text-gray-600 mb-2 truncate" title={product.category}>{product.category}</p>
                  <p className="text-sm text-gray-500 line-clamp-2 break-words">{product.description}</p>
                </div>
                <div className="flex items-center space-x-2">
                  <button
                    onClick={() => openModal(product)}
                    className="p-2 text-blue-600 hover:bg-blue-50 rounded"
                  >
                    <Edit size={16} />
                  </button>
                  <button
                    onClick={() => handleDelete(product.id)}
                    className="p-2 text-red-600 hover:bg-red-50 rounded"
                  >
                    <Trash2 size={16} />
                  </button>

                  <div className="flex justify-between items-center min-w-0">
                    <span className="text-sm text-gray-600">Margin:</span>
                    <span className="font-medium text-gray-800 truncate" title={formatCurrency(product.price - ((product as any).hpp ?? 0))}>{formatCurrency(product.price - ((product as any).hpp ?? 0))}</span>
                  </div>

                </div>
              </div>

              <div className="space-y-3">
                <div className="flex justify-between items-center">

                  <div className="flex justify-between items-center min-w-0">
                    <span className="text-sm text-gray-600">Harga HPP:</span>
                    <span className="font-medium text-gray-800 truncate" title={formatCurrency((product as any).hpp ?? 0)}>{formatCurrency((product as any).hpp ?? 0)}</span>
                  </div>

                  <span className="text-sm text-gray-600">Harga:</span>
                  <span className="font-semibold text-green-600 truncate" title={formatCurrency(product.price)}>{formatCurrency(product.price)}</span>
                </div>

                <div className="flex justify-between items-center">
                  <span className="text-sm text-gray-600">Stok:</span>
                  <div className="flex items-center space-x-2">
                    <span className="font-medium">{product.stock} {getUnitName(product)}</span>
                    <span className={`px-2 py-1 text-xs font-medium rounded-full ${stockStatus.color}`}>
                      {stockStatus.label}
                    </span>
                  </div>
                </div>
              </div>
            </div>
          );
        })}
      </div>

      {filteredProducts.length === 0 && (
        <div className="bg-white rounded-xl shadow-sm border border-gray-100 p-12 text-center">
          <Package size={48} className="mx-auto text-gray-400 mb-4" />
          <h3 className="text-lg font-medium text-gray-900 mb-2">Tidak Ada Produk</h3>
          <p className="text-gray-600 mb-4">
            {searchTerm || selectedCategory
              ? 'Tidak ada produk yang sesuai dengan filter'
              : 'Belum ada produk ditambahkan'}
          </p>
          {!searchTerm && !selectedCategory && (
            <button
              onClick={() => openModal()}
              className="inline-flex items-center px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 transition-colors"
            >
              <Plus size={16} className="mr-2" />
              Tambah Produk Pertama
            </button>
          )}
        </div>
      )}

      {/* Product Modal */}
      {isModalOpen && (
        <div className="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50">
          <div className="relative top-20 mx-auto p-5 border w-11/12 max-w-2xl shadow-lg rounded-lg bg-white">
            <div className="flex items-center justify-between mb-6">
              <h3 className="text-xl font-bold text-gray-900">
                {editingProduct ? 'Edit Produk' : 'Tambah Produk Baru'}
              </h3>
              <button
                onClick={closeModal}
                className="text-gray-400 hover:text-gray-600"
              >
                <X size={24} />
              </button>
            </div>

            <form onSubmit={handleSubmit} className="space-y-6">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Nama Produk *
                  </label>
                  <input
                    type="text"
                    value={formData.name}
                    onChange={(e) => setFormData({ ...formData, name: e.target.value })}
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-green-500 focus:border-green-500"
                    required
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Kategori *
                  </label>
                  <select
                    value={formData.categoryId}
                    onChange={(e) => setFormData({ ...formData, categoryId: e.target.value })}
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-green-500 focus:border-green-500"
                    required
                  >
                    <option value="">Pilih Kategori</option>
                    {categories.map((category) => (
                      <option key={category.id} value={category.id}>
                        {category.name}
                      </option>
                    ))}
                  </select>
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">Harga *</label>
                  <input
                    type="number"
                    value={formData.price}
                    onChange={(e) => setFormData({ ...formData, price: parseInt(e.target.value) || 0 })}
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-green-500 focus:border-green-500"
                    min="0"
                    required
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">Harga HPP *</label>
                  <input
                    type="number"
                    value={(formData as any).hpp}
                    onChange={(e) => setFormData({ ...formData, hpp: parseInt(e.target.value) || 0 })}
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-green-500 focus:border-green-500"
                    min="0"
                    required
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    {editingProduct ? 'Stok Saat Ini' : 'Stok Awal'}
                  </label>
                  <input
                    type="number"
                    value={formData.stock}
                    onChange={(e) => setFormData({ ...formData, stock: parseInt(e.target.value) || 0 })}
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-green-500 focus:border-green-500"
                    min="0"
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Satuan
                  </label>
                  <select
                    value={formData.unit}
                    onChange={(e) => setFormData({ ...formData, unit: e.target.value })}
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-green-500 focus:border-green-500"
                  >
                    <option value="">Pilih Satuan</option>
                    {units.map(unit => (
                      <option key={unit.id} value={unit.id}>
                        {unit.display_text || `${unit.name} - ${unit.content}`}
                      </option>
                    ))}
                  </select>
                </div>
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Deskripsi
                </label>
                <textarea
                  value={formData.description}
                  onChange={(e) => setFormData({ ...formData, description: e.target.value })}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-green-500 focus:border-green-500"
                  rows={4}
                  placeholder="Deskripsi produk..."
                />
              </div>

              <div className="flex justify-end space-x-4">
                <button
                  type="button"
                  onClick={closeModal}
                  className="px-4 py-2 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50"
                >
                  Batal
                </button>
                <button
                  type="submit"
                  className="px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700"
                >
                  {editingProduct ? 'Update Produk' : 'Tambah Produk'}
                </button>
              </div>
            </form>
          </div>
        </div>
      )}
    </div>
  );
};

export default ProductManagement;