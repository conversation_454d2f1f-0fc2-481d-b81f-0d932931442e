import React, { useState } from 'react';
// import { mockProducts, mockStockLogs } from '../services/mockData';
import { apiFetch } from '../services/api';
import { Product, StockLog, Unit } from '../types';
// Simple Toast
const Toast: React.FC<{ type: 'success' | 'error'; message: string }> = ({ type, message }) => (
  <div className={`fixed top-6 right-6 z-50 px-4 py-3 rounded-lg shadow-lg text-sm ${type === 'success' ? 'bg-green-600 text-white' : 'bg-red-600 text-white'}`}>
    {message}
  </div>
);

import { Plus, Minus, Search, Package, History, AlertTriangle, X } from 'lucide-react';

interface StockManagementProps {
  showHistory?: boolean;
}

const StockManagement: React.FC<StockManagementProps> = ({ showHistory = false }) => {
  const [products, setProducts] = useState<Product[]>([]);
  const [stockLogs, setStockLogs] = useState<StockLog[]>([]);
  const [toast, setToast] = useState<{ type: 'success' | 'error'; message: string } | null>(null);

  const [searchTerm, setSearchTerm] = useState('');
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [selectedProduct, setSelectedProduct] = useState<Product | null>(null);
  const [updateType, setUpdateType] = useState<'in' | 'out' | 'adjustment'>('in');
  const [quantity, setQuantity] = useState(0);
  React.useEffect(() => {
    (async () => {
      try {
        const [prodsRes, logsRes] = await Promise.all([
          apiFetch('/api/products'),
          apiFetch('/api/stock-logs'),
        ]);
        const prodItems: any[] = prodsRes.data ?? prodsRes;
        setProducts(prodItems.map((p: any) => ({
          id: String(p.id), name: p.name,
          categoryId: String(p.category_id ?? p.categoryId),
          category: p.category?.name ?? p.category,
          price: +p.price, stock: +p.stock, unit: p.unit ?? '',
          description: p.description ?? '', createdBy: String(p.created_by ?? ''),
          createdAt: p.created_at ?? new Date().toISOString()
        })));
        const logItems: any[] = logsRes.data ?? logsRes;
        setStockLogs(logItems.map((l: any) => ({
          id: String(l.id), productId: String(l.product_id),
          productName: l.product?.name ?? '', type: l.type,
          quantity: Number(l.quantity), previousStock: Number(l.previous_stock),
          newStock: Number(l.new_stock), reason: l.reason ?? '',
          createdBy: String(l.created_by ?? ''), createdAt: l.created_at ?? new Date().toISOString()
        })));
      } catch (e) { console.error(e); }
    })();
  }, []);

  const [reason, setReason] = useState('');
  const [activeTab, setActiveTab] = useState<'stock' | 'history'>(showHistory ? 'history' : 'stock');

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('id-ID', {
      style: 'currency',
      currency: 'IDR',
      minimumFractionDigits: 0
    }).format(amount);
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('id-ID', {
      year: 'numeric',
      month: 'long',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  const getStockStatus = (stock: number) => {
    if (stock <= 10) return { color: 'text-red-600 bg-red-100', label: 'Stok Rendah', icon: AlertTriangle };
    if (stock <= 50) return { color: 'text-orange-600 bg-orange-100', label: 'Stok Sedang', icon: Package };
    return { color: 'text-green-600 bg-green-100', label: 'Stok Cukup', icon: Package };
  };

  const filteredProducts = products.filter(product =>
    product.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
    product.category.toLowerCase().includes(searchTerm.toLowerCase())
  );

  const filteredStockLogs = stockLogs.filter(log =>
    log.productName.toLowerCase().includes(searchTerm.toLowerCase())
  ).sort((a, b) => new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime());

  const openUpdateModal = (product: Product) => {
    setSelectedProduct(product);
    setQuantity(0);
    setReason('');
    setIsModalOpen(true);
  };

  const closeModal = () => {
    setIsModalOpen(false);
    setSelectedProduct(null);
    setQuantity(0);
    setReason('');
  };

  const handleStockUpdate = (e: React.FormEvent) => {
    e.preventDefault();

    if (!selectedProduct || quantity === 0 || !reason.trim()) {
      setToast({ type: 'error', message: 'Semua field wajib diisi dengan benar' });
      setTimeout(() => setToast(null), 2500);
      return;
    }

    (async () => {
      try {
        (e.target as HTMLFormElement).querySelector('button[type="submit"]')?.setAttribute('disabled', 'true');
        const updated = await apiFetch(`/api/products/${selectedProduct!.id}/adjust-stock`, {
          method: 'POST',
          body: JSON.stringify({
            type: updateType,
            quantity,
            reason,
          }),
        });
        setProducts(prev => prev.map(p => p.id === selectedProduct!.id ? {
          ...p,
          stock: Number(updated.stock)
        } : p));
        // Refresh logs quickly
        try {
          const logsRes = await apiFetch('/api/stock-logs');
          const logItems: any[] = logsRes.data ?? logsRes;
          setStockLogs(logItems.map((l: any) => ({
            id: String(l.id), productId: String(l.product_id),
            productName: l.product?.name ?? '', type: l.type,
            quantity: Number(l.quantity), previousStock: Number(l.previous_stock),
            newStock: Number(l.new_stock), reason: l.reason ?? '',
            createdBy: String(l.created_by ?? ''), createdAt: l.created_at ?? new Date().toISOString()
          })));
        } catch { }
        setToast({ type: 'success', message: 'Stok berhasil diupdate!' });
        setTimeout(() => setToast(null), 2500);
        closeModal();
      } catch (e: any) {
        setToast({ type: 'error', message: e.message || 'Gagal update stok' });
        setTimeout(() => setToast(null), 2500);
      } finally {
        (e.target as HTMLFormElement).querySelector('button[type="submit"]')?.removeAttribute('disabled');
      }
    })();
  };

  const getLogTypeLabel = (type: string) => {
    const labels: Record<string, string> = {
      'in': 'Stok Masuk',
      'out': 'Stok Keluar',
      'adjustment': 'Penyesuaian'
    };
    return labels[type] || type;
  };

  const getLogTypeColor = (type: string) => {
    const colors: Record<string, string> = {
      'in': 'text-green-600 bg-green-100',
      'out': 'text-red-600 bg-red-100',
      'adjustment': 'text-blue-600 bg-blue-100'
    };
    return colors[type] || 'text-gray-600 bg-gray-100';
  };

  const getUnitName = (product: Product) => {
    if (typeof product.unit === 'object' && product.unit) {
      return product.unit.name;
    }
    if (product.unit_id) {
      // Note: We don't have units array here, so fallback to unit object or legacy
      return '';
    }
    return product.unit_legacy || product.unit || '';
  };

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <h1 className="text-3xl font-bold text-gray-900">
          {showHistory ? 'History Stok' : 'Management Stok'}
          {toast && <Toast type={toast.type} message={toast.message} />}

        </h1>

        {!showHistory && (
          <div className="flex items-center space-x-2 bg-white rounded-lg p-1 border border-gray-200">
            <button
              onClick={() => setActiveTab('stock')}
              className={`px-4 py-2 rounded-md text-sm font-medium transition-colors ${activeTab === 'stock'
                ? 'bg-green-100 text-green-700'
                : 'text-gray-500 hover:text-gray-700'
                }`}
            >
              <Package size={16} className="inline mr-2" />
              Kelola Stok
            </button>
            <button
              onClick={() => setActiveTab('history')}
              className={`px-4 py-2 rounded-md text-sm font-medium transition-colors ${activeTab === 'history'
                ? 'bg-green-100 text-green-700'
                : 'text-gray-500 hover:text-gray-700'
                }`}
            >
              <History size={16} className="inline mr-2" />
              History
            </button>
          </div>
        )}
      </div>

      {/* Search */}
      <div className="bg-white p-6 rounded-xl shadow-sm border border-gray-100">
        <div className="relative max-w-md">
          <Search size={20} className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400" />
          <input
            type="text"
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            className="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-green-500 focus:border-green-500"
            placeholder={activeTab === 'stock' ? 'Cari produk...' : 'Cari history stok...'}
          />
        </div>
      </div>

      {/* Stock Management Tab */}
      {(activeTab === 'stock' || showHistory) && !showHistory && (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {filteredProducts.map((product) => {
            const stockStatus = getStockStatus(product.stock);
            const IconComponent = stockStatus.icon;

            return (
              <div key={product.id} className="bg-white rounded-xl shadow-sm border border-gray-100 p-6 hover:shadow-md transition-shadow">
                <div className="flex items-start justify-between mb-4">
                  <div className="flex-1">
                    <h3 className="text-lg font-semibold text-gray-900 mb-1">{product.name}</h3>
                    <p className="text-sm text-gray-600 mb-2">{product.category}</p>
                    <p className="text-sm text-gray-500">{formatCurrency(product.price)}</p>
                  </div>
                  <div className={`flex items-center px-3 py-1 text-sm font-medium rounded-full ${stockStatus.color}`}>
                    <IconComponent size={16} className="mr-1" />
                    {stockStatus.label}
                  </div>
                </div>

                <div className="space-y-3">
                  <div className="flex justify-between items-center">
                    <span className="text-sm text-gray-600">Stok Tersedia:</span>
                    <span className="text-lg font-bold text-gray-900">
                      {product.stock} {getUnitName(product)}
                    </span>
                  </div>

                  <button
                    onClick={() => openUpdateModal(product)}
                    className="w-full px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 transition-colors"
                  >
                    Update Stok
                  </button>
                </div>
              </div>
            );
          })}
        </div>
      )}

      {/* History Tab */}
      {(activeTab === 'history' || showHistory) && (
        <div className="bg-white rounded-xl shadow-sm border border-gray-100 overflow-hidden">
          <div className="overflow-x-auto">
            <table className="min-w-full divide-y divide-gray-200">
              <thead className="bg-gray-50">
                <tr>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Produk
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Tipe
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Quantity
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Stok Sebelum
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Stok Sesudah
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Alasan
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Tanggal
                  </th>
                </tr>
              </thead>
              <tbody className="bg-white divide-y divide-gray-200">
                {filteredStockLogs.map((log) => (
                  <tr key={log.id} className="hover:bg-gray-50">
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div className="text-sm font-medium text-gray-900">{log.productName}</div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${getLogTypeColor(log.type)}`}>
                        {getLogTypeLabel(log.type)}
                      </span>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div className={`text-sm font-medium ${log.quantity > 0 ? 'text-green-600' : 'text-red-600'}`}>
                        {log.quantity > 0 ? '+' : ''}{log.quantity}
                      </div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                      {log.previousStock}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">
                      {log.newStock}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                      {log.reason}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                      {formatDate(log.createdAt)}
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>

          {filteredStockLogs.length === 0 && (
            <div className="text-center py-12 text-gray-500">
              Tidak ada history stok yang ditemukan
            </div>
          )}
        </div>
      )}

      {/* Update Stock Modal */}
      {isModalOpen && selectedProduct && (
        <div className="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50">
          <div className="relative top-20 mx-auto p-5 border w-11/12 max-w-md shadow-lg rounded-lg bg-white">
            <div className="flex items-center justify-between mb-6">
              <h3 className="text-xl font-bold text-gray-900">
                Update Stok - {selectedProduct.name}
              </h3>
              <button
                onClick={closeModal}
                className="text-gray-400 hover:text-gray-600"
              >
                <X size={24} />
              </button>
            </div>

            <div className="mb-4 p-3 bg-blue-50 border border-blue-200 rounded-lg">
              <div className="text-sm text-blue-800">
                <strong>Stok Saat Ini:</strong> {selectedProduct.stock} {getUnitName(selectedProduct)}
              </div>
            </div>

            <form onSubmit={handleStockUpdate} className="space-y-6">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Tipe Update *
                </label>
                <select
                  value={updateType}
                  onChange={(e) => setUpdateType(e.target.value as 'in' | 'out' | 'adjustment')}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-green-500 focus:border-green-500"
                >
                  <option value="in">Stok Masuk</option>
                  <option value="out">Stok Keluar</option>
                  <option value="adjustment">Penyesuaian</option>
                </select>
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Quantity *
                </label>
                <div className="flex items-center space-x-2">
                  <button
                    type="button"
                    onClick={() => setQuantity(updateType === 'adjustment' ? quantity - 1 : Math.max(0, quantity - 1))}
                    className="p-2 border border-gray-300 rounded-lg hover:bg-gray-100"
                  >
                    <Minus size={16} />
                  </button>
                  <input
                    type="number"
                    value={quantity}
                    onChange={(e) => setQuantity(parseInt(e.target.value) || 0)}
                    className="flex-1 px-3 py-2 border border-gray-300 rounded-lg focus:ring-green-500 focus:border-green-500 text-center"
                    min={updateType === 'adjustment' ? undefined : 0}
                    required
                  />
                  <button
                    type="button"
                    onClick={() => setQuantity(quantity + 1)}
                    className="p-2 border border-gray-300 rounded-lg hover:bg-gray-100"
                  >
                    <Plus size={16} />
                  </button>
                </div>
                {updateType === 'adjustment' && (
                  <p className="text-xs text-gray-500 mt-1">
                    Gunakan angka positif untuk menambah, negatif untuk mengurangi
                  </p>
                )}
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Alasan *
                </label>
                <textarea
                  value={reason}
                  onChange={(e) => setReason(e.target.value)}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-green-500 focus:border-green-500"
                  rows={3}
                  placeholder="Berikan alasan untuk update stok..."
                  required
                />
              </div>

              <div className="flex justify-end space-x-4">
                <button
                  type="button"
                  onClick={closeModal}
                  className="px-4 py-2 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50"
                >
                  Batal
                </button>
                <button
                  type="submit"
                  className="px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700"
                >
                  Update Stok
                </button>
              </div>
            </form>
          </div>
        </div>
      )}
    </div>
  );
};

export default StockManagement;