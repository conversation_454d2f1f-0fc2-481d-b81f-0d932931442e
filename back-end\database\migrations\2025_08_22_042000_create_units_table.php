<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('units', function (Blueprint $table) {
            $table->id();
            $table->string('name'); // Nama satuan (dus, karton, box, dll)
            $table->string('content'); // Isi dalam satuan (12 botol, 24 pcs, dll)
            $table->integer('quantity'); // Jumlah isi dalam angka (12, 24, dll)
            $table->string('base_unit'); // <PERSON><PERSON><PERSON> dasar (botol, pcs, kg, liter, dll)
            $table->text('description')->nullable(); // Deskripsi tambahan
            $table->enum('status', ['active', 'inactive'])->default('active'); // Status satuan
            $table->foreignId('created_by')->constrained('users')->cascadeOnDelete(); // Super admin yang membuat
            $table->timestamps();
            
            // Index untuk performa
            $table->index(['status', 'name']);
            $table->index('created_by');
            
            // Unique constraint untuk nama satuan
            $table->unique('name');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('units');
    }
};
