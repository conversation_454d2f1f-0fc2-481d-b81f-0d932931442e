import React, { useState, useEffect } from 'react';
import { Plus, Edit, Trash2, Gift, Calendar, Users, Eye } from 'lucide-react';
import { apiFetch } from '../services/api';

interface Category {
  id: number;
  name: string;
}

interface Product {
  id: number;
  name: string;
  category_id: number;
}

interface Bonus {
  id: number;
  name: string;
  category_id: number;
  product_id?: number;
  minimum_quantity: number;
  bonus_description: string;
  type: 'periode' | 'quota';
  start_date?: string;
  end_date?: string;
  quota_total?: number;
  quota_used: number;
  status: 'active' | 'inactive' | 'expired';
  category: Category;
  product?: Product;
  remaining_quota?: number;
  type_name: string;
  status_name: string;
}

interface BonusClaim {
  id: number;
  bonus_id: number;
  transaction_id: number;
  mitra_id: number;
  quantity_purchased: number;
  bonus_received: string;
  status: 'pending' | 'claimed' | 'expired';
  created_at: string;
  bonus: {
    id: number;
    name: string;
    bonus_description: string;
  };
  mitra: {
    id: number;
    name: string;
    phone: string;
  };
  transaction: {
    id: number;
    total: number;
  };
}

const BonusManagement: React.FC = () => {
  const [bonuses, setBonuses] = useState<Bonus[]>([]);
  const [categories, setCategories] = useState<Category[]>([]);
  const [products, setProducts] = useState<Product[]>([]);
  const [claims, setClaims] = useState<BonusClaim[]>([]);
  const [loading, setLoading] = useState(true);
  const [showForm, setShowForm] = useState(false);
  const [showClaims, setShowClaims] = useState(false);
  const [editingBonus, setEditingBonus] = useState<Bonus | null>(null);
  const [selectedCategoryId, setSelectedCategoryId] = useState<number | null>(null);

  const [formData, setFormData] = useState({
    name: '',
    category_id: '',
    product_id: '',
    minimum_quantity: '',
    bonus_description: '',
    type: 'periode' as 'periode' | 'quota',
    start_date: '',
    end_date: '',
    quota_total: '',
    status: 'active' as 'active' | 'inactive'
  });

  useEffect(() => {
    fetchBonuses();
    fetchCategories();
    fetchClaims();
  }, []);

  useEffect(() => {
    if (selectedCategoryId) {
      fetchProducts(selectedCategoryId);
    } else {
      setProducts([]);
    }
  }, [selectedCategoryId]);

  const fetchBonuses = async () => {
    try {
      const data = await apiFetch('/api/bonuses');
      setBonuses(data.data || []);
    } catch (error) {
      console.error('Error fetching bonuses:', error);
    } finally {
      setLoading(false);
    }
  };

  const fetchCategories = async () => {
    try {
      const data = await apiFetch('/api/categories');
      setCategories(data.data || []);
    } catch (error) {
      console.error('Error fetching categories:', error);
    }
  };

  const fetchProducts = async (categoryId: number) => {
    try {
      const data = await apiFetch(`/api/products?category_id=${categoryId}`);
      setProducts(data.data || []);
    } catch (error) {
      console.error('Error fetching products:', error);
    }
  };

  const fetchClaims = async () => {
    try {
      const data = await apiFetch('/api/bonus-claims');
      setClaims(data.data || []);
    } catch (error) {
      console.error('Error fetching claims:', error);
    }
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    try {
      const url = editingBonus ? `/api/bonuses/${editingBonus.id}` : '/api/bonuses';
      const method = editingBonus ? 'PUT' : 'POST';

      await apiFetch(url, {
        method,
        body: JSON.stringify({
          ...formData,
          category_id: parseInt(formData.category_id),
          product_id: formData.product_id ? parseInt(formData.product_id) : null,
          minimum_quantity: parseInt(formData.minimum_quantity),
          quota_total: formData.quota_total ? parseInt(formData.quota_total) : null
        })
      });

      await fetchBonuses();
      resetForm();
      alert(editingBonus ? 'Bonus berhasil diperbarui!' : 'Bonus berhasil dibuat!');
    } catch (error) {
      console.error('Error saving bonus:', error);
      alert('Terjadi kesalahan saat menyimpan bonus');
    }
  };

  const handleEdit = (bonus: Bonus) => {
    setEditingBonus(bonus);
    setFormData({
      name: bonus.name,
      category_id: bonus.category_id.toString(),
      product_id: bonus.product_id?.toString() || '',
      minimum_quantity: bonus.minimum_quantity.toString(),
      bonus_description: bonus.bonus_description,
      type: bonus.type,
      start_date: bonus.start_date || '',
      end_date: bonus.end_date || '',
      quota_total: bonus.quota_total?.toString() || '',
      status: bonus.status
    });
    setSelectedCategoryId(bonus.category_id);
    setShowForm(true);
  };

  const handleDelete = async (id: number) => {
    if (!confirm('Apakah Anda yakin ingin menghapus bonus ini?')) return;

    try {
      await apiFetch(`/api/bonuses/${id}`, {
        method: 'DELETE'
      });

      await fetchBonuses();
      alert('Bonus berhasil dihapus!');
    } catch (error) {
      console.error('Error deleting bonus:', error);
      alert('Terjadi kesalahan saat menghapus bonus');
    }
  };

  const resetForm = () => {
    setFormData({
      name: '',
      category_id: '',
      product_id: '',
      minimum_quantity: '',
      bonus_description: '',
      type: 'periode',
      start_date: '',
      end_date: '',
      quota_total: '',
      status: 'active'
    });
    setEditingBonus(null);
    setSelectedCategoryId(null);
    setShowForm(false);
  };

  const handleCategoryChange = (categoryId: string) => {
    setFormData({ ...formData, category_id: categoryId, product_id: '' });
    setSelectedCategoryId(categoryId ? parseInt(categoryId) : null);
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'active': return 'bg-green-100 text-green-800';
      case 'inactive': return 'bg-gray-100 text-gray-800';
      case 'expired': return 'bg-red-100 text-red-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  const getClaimStatusColor = (status: string) => {
    switch (status) {
      case 'pending': return 'bg-yellow-100 text-yellow-800';
      case 'claimed': return 'bg-green-100 text-green-800';
      case 'expired': return 'bg-red-100 text-red-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  if (loading) {
    return (
      <div className="flex justify-center items-center h-64">
        <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-blue-500"></div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <h1 className="text-2xl font-bold text-gray-900">Manajemen Bonus</h1>
        <div className="flex space-x-2">
          <button
            onClick={() => setShowClaims(!showClaims)}
            className="bg-purple-600 hover:bg-purple-700 text-white px-4 py-2 rounded-lg flex items-center space-x-2"
          >
            <Eye className="h-4 w-4" />
            <span>{showClaims ? 'Sembunyikan' : 'Lihat'} Klaim</span>
          </button>
          <button
            onClick={() => setShowForm(true)}
            className="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg flex items-center space-x-2"
          >
            <Plus className="h-4 w-4" />
            <span>Tambah Bonus</span>
          </button>
        </div>
      </div>

      {/* Form Modal */}
      {showForm && (
        <div className="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50">
          <div className="relative top-20 mx-auto p-5 border w-11/12 md:w-3/4 lg:w-1/2 shadow-lg rounded-md bg-white">
            <div className="mt-3">
              <h3 className="text-lg font-medium text-gray-900 mb-4">
                {editingBonus ? 'Edit Bonus' : 'Tambah Bonus Baru'}
              </h3>

              <form onSubmit={handleSubmit} className="space-y-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700">Nama Event</label>
                  <input
                    type="text"
                    value={formData.name}
                    onChange={(e) => setFormData({ ...formData, name: e.target.value })}
                    className="mt-1 block w-full border border-gray-300 rounded-md px-3 py-2"
                    required
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700">Kategori Produk</label>
                  <select
                    value={formData.category_id}
                    onChange={(e) => handleCategoryChange(e.target.value)}
                    className="mt-1 block w-full border border-gray-300 rounded-md px-3 py-2"
                    required
                  >
                    <option value="">Pilih Kategori</option>
                    {categories.map((category) => (
                      <option key={category.id} value={category.id}>
                        {category.name}
                      </option>
                    ))}
                  </select>
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700">Produk (Opsional)</label>
                  <select
                    value={formData.product_id}
                    onChange={(e) => setFormData({ ...formData, product_id: e.target.value })}
                    className="mt-1 block w-full border border-gray-300 rounded-md px-3 py-2"
                    disabled={!selectedCategoryId}
                  >
                    <option value="">Semua produk dalam kategori</option>
                    {products.map((product) => (
                      <option key={product.id} value={product.id}>
                        {product.name}
                      </option>
                    ))}
                  </select>
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700">Jumlah Pembelian Minimum</label>
                  <input
                    type="number"
                    value={formData.minimum_quantity}
                    onChange={(e) => setFormData({ ...formData, minimum_quantity: e.target.value })}
                    className="mt-1 block w-full border border-gray-300 rounded-md px-3 py-2"
                    min="1"
                    required
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700">Bonus</label>
                  <textarea
                    value={formData.bonus_description}
                    onChange={(e) => setFormData({ ...formData, bonus_description: e.target.value })}
                    className="mt-1 block w-full border border-gray-300 rounded-md px-3 py-2"
                    rows={3}
                    required
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">Jenis Bonus</label>
                  <div className="space-y-2">
                    <label className="flex items-center">
                      <input
                        type="radio"
                        value="periode"
                        checked={formData.type === 'periode'}
                        onChange={(e) => setFormData({ ...formData, type: e.target.value as 'periode' | 'quota' })}
                        className="mr-2"
                      />
                      Berdasarkan Periode
                    </label>
                    <label className="flex items-center">
                      <input
                        type="radio"
                        value="quota"
                        checked={formData.type === 'quota'}
                        onChange={(e) => setFormData({ ...formData, type: e.target.value as 'periode' | 'quota' })}
                        className="mr-2"
                      />
                      Berdasarkan Quota
                    </label>
                  </div>
                </div>

                {formData.type === 'periode' && (
                  <div className="grid grid-cols-2 gap-4">
                    <div>
                      <label className="block text-sm font-medium text-gray-700">Tanggal Mulai</label>
                      <input
                        type="date"
                        value={formData.start_date}
                        onChange={(e) => setFormData({ ...formData, start_date: e.target.value })}
                        className="mt-1 block w-full border border-gray-300 rounded-md px-3 py-2"
                        required
                      />
                    </div>
                    <div>
                      <label className="block text-sm font-medium text-gray-700">Tanggal Sampai</label>
                      <input
                        type="date"
                        value={formData.end_date}
                        onChange={(e) => setFormData({ ...formData, end_date: e.target.value })}
                        className="mt-1 block w-full border border-gray-300 rounded-md px-3 py-2"
                        required
                      />
                    </div>
                  </div>
                )}

                {formData.type === 'quota' && (
                  <div>
                    <label className="block text-sm font-medium text-gray-700">Jumlah Quota</label>
                    <input
                      type="number"
                      value={formData.quota_total}
                      onChange={(e) => setFormData({ ...formData, quota_total: e.target.value })}
                      className="mt-1 block w-full border border-gray-300 rounded-md px-3 py-2"
                      min="1"
                      required
                    />
                  </div>
                )}

                <div>
                  <label className="block text-sm font-medium text-gray-700">Status</label>
                  <select
                    value={formData.status}
                    onChange={(e) => setFormData({ ...formData, status: e.target.value as 'active' | 'inactive' })}
                    className="mt-1 block w-full border border-gray-300 rounded-md px-3 py-2"
                  >
                    <option value="active">Aktif</option>
                    <option value="inactive">Tidak Aktif</option>
                  </select>
                </div>

                <div className="flex justify-end space-x-2 pt-4">
                  <button
                    type="button"
                    onClick={resetForm}
                    className="px-4 py-2 border border-gray-300 rounded-md text-gray-700 hover:bg-gray-50"
                  >
                    Batal
                  </button>
                  <button
                    type="submit"
                    className="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700"
                  >
                    {editingBonus ? 'Perbarui' : 'Simpan'}
                  </button>
                </div>
              </form>
            </div>
          </div>
        </div>
      )}

      {/* Bonus List */}
      <div className="bg-white shadow rounded-lg">
        <div className="px-4 py-5 sm:p-6">
          <h3 className="text-lg leading-6 font-medium text-gray-900 mb-4">Daftar Bonus</h3>

          {bonuses.length === 0 ? (
            <div className="text-center py-8">
              <Gift className="mx-auto h-12 w-12 text-gray-400" />
              <h3 className="mt-2 text-sm font-medium text-gray-900">Belum ada bonus</h3>
              <p className="mt-1 text-sm text-gray-500">Mulai dengan membuat bonus pertama Anda.</p>
            </div>
          ) : (
            <div className="overflow-x-auto">
              <table className="min-w-full divide-y divide-gray-200">
                <thead className="bg-gray-50">
                  <tr>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Event
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Kategori/Produk
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Syarat
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Jenis
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Status
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Aksi
                    </th>
                  </tr>
                </thead>
                <tbody className="bg-white divide-y divide-gray-200">
                  {bonuses.map((bonus) => (
                    <tr key={bonus.id}>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div>
                          <div className="text-sm font-medium text-gray-900">{bonus.name}</div>
                          <div className="text-sm text-gray-500">{bonus.bonus_description}</div>
                        </div>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div className="text-sm text-gray-900">{bonus.category.name}</div>
                        {bonus.product && (
                          <div className="text-sm text-gray-500">{bonus.product.name}</div>
                        )}
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                        Min. {bonus.minimum_quantity} unit
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div className="text-sm text-gray-900">{bonus.type_name}</div>
                        {bonus.type === 'periode' && bonus.start_date && bonus.end_date && (
                          <div className="text-sm text-gray-500">
                            {new Date(bonus.start_date).toLocaleDateString()} - {new Date(bonus.end_date).toLocaleDateString()}
                          </div>
                        )}
                        {bonus.type === 'quota' && (
                          <div className="text-sm text-gray-500">
                            {bonus.quota_used}/{bonus.quota_total} digunakan
                          </div>
                        )}
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${getStatusColor(bonus.status)}`}>
                          {bonus.status_name}
                        </span>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm font-medium">
                        <div className="flex space-x-2">
                          <button
                            onClick={() => handleEdit(bonus)}
                            className="text-indigo-600 hover:text-indigo-900"
                          >
                            <Edit className="h-4 w-4" />
                          </button>
                          <button
                            onClick={() => handleDelete(bonus.id)}
                            className="text-red-600 hover:text-red-900"
                          >
                            <Trash2 className="h-4 w-4" />
                          </button>
                        </div>
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          )}
        </div>
      </div>

      {/* Claims List */}
      {showClaims && (
        <div className="bg-white shadow rounded-lg">
          <div className="px-4 py-5 sm:p-6">
            <h3 className="text-lg leading-6 font-medium text-gray-900 mb-4">Klaim Bonus</h3>

            {claims.length === 0 ? (
              <div className="text-center py-8">
                <Users className="mx-auto h-12 w-12 text-gray-400" />
                <h3 className="mt-2 text-sm font-medium text-gray-900">Belum ada klaim</h3>
                <p className="mt-1 text-sm text-gray-500">Klaim bonus akan muncul ketika mitra memenuhi syarat.</p>
              </div>
            ) : (
              <div className="overflow-x-auto">
                <table className="min-w-full divide-y divide-gray-200">
                  <thead className="bg-gray-50">
                    <tr>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Bonus
                      </th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Mitra
                      </th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Transaksi
                      </th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Jumlah Beli
                      </th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Status
                      </th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Tanggal
                      </th>
                    </tr>
                  </thead>
                  <tbody className="bg-white divide-y divide-gray-200">
                    {claims.map((claim) => (
                      <tr key={claim.id}>
                        <td className="px-6 py-4 whitespace-nowrap">
                          <div>
                            <div className="text-sm font-medium text-gray-900">{claim.bonus.name}</div>
                            <div className="text-sm text-gray-500">{claim.bonus_received}</div>
                          </div>
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap">
                          <div>
                            <div className="text-sm font-medium text-gray-900">{claim.mitra.name}</div>
                            <div className="text-sm text-gray-500">{claim.mitra.phone}</div>
                          </div>
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap">
                          <div>
                            <div className="text-sm font-medium text-gray-900">TXN-{claim.transaction.id}</div>
                            <div className="text-sm text-gray-500">Rp {claim.transaction.total.toLocaleString()}</div>
                          </div>
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                          {claim.quantity_purchased} unit
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap">
                          <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${getClaimStatusColor(claim.status)}`}>
                            {claim.status === 'pending' ? 'Menunggu' : claim.status === 'claimed' ? 'Diklaim' : 'Kedaluwarsa'}
                          </span>
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                          {new Date(claim.created_at).toLocaleDateString()}
                        </td>
                      </tr>
                    ))}
                  </tbody>
                </table>
              </div>
            )}
          </div>
        </div>
      )}
    </div>
  );
};

export default BonusManagement;
