<?php

use Illuminate\Support\Facades\Route;

use App\Http\Controllers\AuthController;

Route::get('/health', fn() => response()->json(['ok' => true]));
// CORS preflight handler for all API routes (helps when proxy blocks preflight to PHP)
Route::options('/{any}', function () {
    return response()->noContent(204);
})->where('any', '.*');


// Public WhatsApp proxy (no auth) to avoid CORS
Route::post('/whatsapp/send', [\App\Http\Controllers\WhatsAppProxyController::class, 'send']);

// Auth
Route::post('/auth/login', [AuthController::class, 'login']);
Route::middleware('auth:sanctum')->group(function () {
    Route::post('/auth/logout', [AuthController::class, 'logout']);
    Route::get('/auth/me', [AuthController::class, 'me']);

    // Dashboard
    Route::get('/dashboard/metrics', [\App\Http\Controllers\DashboardController::class, 'metrics']);

    // Analytics
    Route::get('/analytics/products/monthly', [\App\Http\Controllers\AnalyticsController::class, 'productMonthly']);
    Route::get('/analytics/top-products', [\App\Http\Controllers\AnalyticsController::class, 'topProducts']);
    Route::get('/analytics/top-sales', [\App\Http\Controllers\AnalyticsController::class, 'topSales']);
    Route::get('/analytics/top-outlets', [\App\Http\Controllers\AnalyticsController::class, 'topOutlets']);
    Route::get('/analytics/outlet-products', [\App\Http\Controllers\AnalyticsController::class, 'outletProducts']);

    // Categories
    Route::get('/categories', [\App\Http\Controllers\CategoryController::class, 'index']);
    Route::post('/categories', [\App\Http\Controllers\CategoryController::class, 'store']);
    Route::put('/categories/{category}', [\App\Http\Controllers\CategoryController::class, 'update']);
    Route::delete('/categories/{category}', [\App\Http\Controllers\CategoryController::class, 'destroy']);

    // Receivable payments
    Route::get('/receivable-payments', [\App\Http\Controllers\ReceivablePaymentController::class, 'index']);
    Route::post('/receivable-payments', [\App\Http\Controllers\ReceivablePaymentController::class, 'store']);

    // Products
    Route::get('/products', [\App\Http\Controllers\ProductController::class, 'index']);
    Route::post('/products', [\App\Http\Controllers\ProductController::class, 'store']);
    Route::put('/products/{product}', [\App\Http\Controllers\ProductController::class, 'update']);
    Route::delete('/products/{product}', [\App\Http\Controllers\ProductController::class, 'destroy']);
    Route::post('/products/{product}/adjust-stock', [\App\Http\Controllers\ProductController::class, 'adjustStock']);

    // Placeholder secured group
    Route::get('/secure/ping', fn() => response()->json(['pong' => true]));
    // Mitras
    Route::get('/mitras', [\App\Http\Controllers\MitraController::class, 'index']);
    Route::post('/mitras', [\App\Http\Controllers\MitraController::class, 'store']);
    Route::put('/mitras/{mitra}', [\App\Http\Controllers\MitraController::class, 'update']);
    Route::delete('/mitras/{mitra}', [\App\Http\Controllers\MitraController::class, 'destroy']);
    // Stock logs
    Route::get('/stock-logs', [\App\Http\Controllers\StockLogController::class, 'index']);
    // Transactions
    Route::post('/transactions', [\App\Http\Controllers\TransactionCreateController::class, 'store']);

    Route::get('/transactions', [\App\Http\Controllers\TransactionController::class, 'index']);
    Route::post('/transactions/{transaction}/approve-owner', [\App\Http\Controllers\TransactionController::class, 'approveOwner']);
    Route::post('/transactions/{transaction}/reject', [\App\Http\Controllers\TransactionController::class, 'reject']);
    Route::post('/transactions/{transaction}/approve-gudang', [\App\Http\Controllers\TransactionController::class, 'approveGudang']);
    Route::post('/transactions/{transaction}/ship', [\App\Http\Controllers\TransactionController::class, 'ship']);
    Route::post('/transactions/{transaction}/deliver', [\App\Http\Controllers\TransactionController::class, 'deliver']);
    Route::get('/transactions/{transaction}/recipients', [\App\Http\Controllers\TransactionController::class, 'recipients']);

    // Transaction Logs
    Route::get('/transaction-logs', [\App\Http\Controllers\TransactionLogController::class, 'index']);
    Route::get('/transaction-logs/transaction/{transactionId}', [\App\Http\Controllers\TransactionLogController::class, 'showForTransaction']);
    Route::get('/transaction-logs/stats', [\App\Http\Controllers\TransactionLogController::class, 'stats']);
    Route::get('/transaction-logs/recent', [\App\Http\Controllers\TransactionLogController::class, 'recent']);
    Route::get('/transaction-logs/export', [\App\Http\Controllers\TransactionLogController::class, 'export']);

    // Bonus Management
    Route::get('/bonuses', [\App\Http\Controllers\BonusController::class, 'index']);
    Route::post('/bonuses', [\App\Http\Controllers\BonusController::class, 'store']);
    Route::get('/bonuses/{bonus}', [\App\Http\Controllers\BonusController::class, 'show']);
    Route::put('/bonuses/{bonus}', [\App\Http\Controllers\BonusController::class, 'update']);
    Route::delete('/bonuses/{bonus}', [\App\Http\Controllers\BonusController::class, 'destroy']);
    Route::get('/bonus-claims', [\App\Http\Controllers\BonusController::class, 'claims']);
    Route::get('/bonus-stats', [\App\Http\Controllers\BonusController::class, 'stats']);

    // Master Satuan (Units)
    Route::get('/units', [\App\Http\Controllers\UnitController::class, 'index']); // Super admin only
    Route::get('/units/list', [\App\Http\Controllers\UnitController::class, 'list']); // All roles for dropdown
    Route::post('/units', [\App\Http\Controllers\UnitController::class, 'store']); // Super admin only
    Route::get('/units/{unit}', [\App\Http\Controllers\UnitController::class, 'show']); // Super admin only
    Route::put('/units/{unit}', [\App\Http\Controllers\UnitController::class, 'update']); // Super admin only
    Route::delete('/units/{unit}', [\App\Http\Controllers\UnitController::class, 'destroy']); // Super admin only
    Route::get('/units-stats', [\App\Http\Controllers\UnitController::class, 'stats']); // Super admin only
    Route::post('/units/bulk-status', [\App\Http\Controllers\UnitController::class, 'bulkUpdateStatus']); // Super admin only
    Route::get('/units/export', [\App\Http\Controllers\UnitController::class, 'export']); // Super admin only

    // Reward Management (Owner only)
    Route::get('/rewards', [\App\Http\Controllers\RewardController::class, 'index']); // Daftar reward
    Route::post('/rewards', [\App\Http\Controllers\RewardController::class, 'store']); // Buat reward baru
    Route::get('/rewards/{reward}', [\App\Http\Controllers\RewardController::class, 'show']); // Detail reward
    Route::put('/rewards/{reward}', [\App\Http\Controllers\RewardController::class, 'update']); // Update reward
    Route::delete('/rewards/{reward}', [\App\Http\Controllers\RewardController::class, 'destroy']); // Hapus reward
    Route::get('/rewards/{reward}/leaderboard', [\App\Http\Controllers\RewardController::class, 'leaderboard']); // Leaderboard reward
    Route::get('/rewards-stats', [\App\Http\Controllers\RewardController::class, 'stats']); // Statistik reward
    Route::get('/mitra/{mitraId}/reward-progress', [\App\Http\Controllers\RewardController::class, 'mitraProgress']); // Progress mitra



    // Owner settings
    Route::get('/settings', [\App\Http\Controllers\SettingController::class, 'show']);
    Route::put('/settings', [\App\Http\Controllers\SettingController::class, 'update']);
    Route::post('/settings/logo', [\App\Http\Controllers\SettingController::class, 'uploadLogo']);

    // Users (superadmin scope ideally; for now simple)
    Route::get('/users', [\App\Http\Controllers\UserController::class, 'index']);
    Route::post('/users', [\App\Http\Controllers\UserController::class, 'store']);
    Route::put('/users/{user}', [\App\Http\Controllers\UserController::class, 'update']);
    Route::delete('/users/{user}', [\App\Http\Controllers\UserController::class, 'destroy']);

    // Invoice PDF
    Route::get('/transactions/{transaction}/invoice.pdf', [\App\Http\Controllers\PdfInvoiceController::class, 'show']);
});
