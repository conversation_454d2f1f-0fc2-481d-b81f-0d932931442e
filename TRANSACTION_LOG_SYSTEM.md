# Sistem Logging Aktivitas Admin Gudang

## Deskripsi
Sistem ini mencatat setiap aksi yang dilakukan oleh admin gudang pada transaksi (approve, reject, ship, deliver) sehingga owner dapat memantau siapa yang mengubah status transaksi dan kapan perubahan tersebut terjadi.

## Fitur Utama

### 1. Pencatatan Log Otomatis
- **Approve Gudang**: Ketika admin gudang menyetujui transaksi
- **Reject**: Ketika admin gudang menolak transaksi (dengan alasan)
- **Ship**: Ketika admin gudang mengirim barang (dengan nomor resi)
- **Deliver**: Ketika admin gudang menyelesaikan pengiriman

### 2. Monitoring untuk Owner
- Melihat semua aktivitas admin gudang
- Filter berdasarkan transaksi, admin, atau jenis aksi
- Statistik aktivitas dalam periode tertentu
- Export data log ke CSV

### 3. Audit Trail Lengkap
- Siapa yang melakukan aksi
- <PERSON><PERSON> aksi dilakukan
- Status sebelum dan sesudah
- <PERSON>atan tambahan (alasan reject, nomor resi, dll)
- Metadata lengkap untuk analisis

## Komponen Sistem

### 1. Database Schema (`transaction_logs`)
```sql
- id: Primary key
- transaction_id: Foreign key ke transactions
- user_id: Foreign key ke users (admin gudang)
- action: Jenis aksi (approve_gudang, reject, ship, deliver)
- previous_status: Status sebelumnya
- new_status: Status baru
- notes: Catatan tambahan
- metadata: Data tambahan dalam JSON
- created_at, updated_at: Timestamp
```

### 2. Model TransactionLog
- Relasi ke Transaction dan User
- Scope untuk filtering
- Accessor untuk nama aksi yang user-friendly

### 3. TransactionLogService
- `logApproveGudang()`: Log persetujuan gudang
- `logReject()`: Log penolakan dengan alasan
- `logShip()`: Log pengiriman dengan nomor resi
- `logDeliver()`: Log penyelesaian pengiriman
- `getLogsForTransaction()`: Ambil log untuk transaksi tertentu
- `getLogsForOwner()`: Ambil log untuk owner tertentu
- `getActivityStats()`: Statistik aktivitas
- `getMostActiveAdmins()`: Admin paling aktif

### 4. TransactionLogController
- API endpoints untuk owner mengakses log
- Filter dan pagination
- Export ke CSV

### 5. Modifikasi TransactionController
- Integrasi logging pada setiap aksi admin gudang
- Dependency injection TransactionLogService

## API Endpoints

### 1. Daftar Log Aktivitas
```
GET /api/transaction-logs
```
**Parameters:**
- `per_page`: Jumlah data per halaman (default: 20)
- `transaction_id`: Filter berdasarkan transaksi tertentu
- `action`: Filter berdasarkan jenis aksi
- `user_id`: Filter berdasarkan admin gudang tertentu

**Response:**
```json
{
  "data": [
    {
      "id": 1,
      "transaction_id": 123,
      "action": "approve_gudang",
      "action_name": "Menyetujui Transaksi",
      "previous_status": "pending_gudang",
      "new_status": "approved",
      "status_name": "Disetujui",
      "notes": "Transaksi disetujui oleh admin gudang",
      "user": {
        "id": 5,
        "name": "Admin Gudang A",
        "role": "admin_gudang"
      },
      "transaction": {
        "id": 123,
        "total": 1250000,
        "mitra": {
          "name": "PT Tani Sejahtera"
        }
      },
      "created_at": "2024-08-22T10:30:00Z"
    }
  ],
  "meta": {
    "current_page": 1,
    "total": 25
  }
}
```

### 2. Log untuk Transaksi Tertentu
```
GET /api/transaction-logs/transaction/{transactionId}
```

### 3. Statistik Aktivitas
```
GET /api/transaction-logs/stats?days=30
```
**Response:**
```json
{
  "stats": {
    "approve_gudang": 15,
    "reject": 3,
    "ship": 12,
    "deliver": 10,
    "total": 40,
    "period_days": 30
  },
  "most_active_admins": [
    {
      "user_id": 5,
      "activity_count": 25,
      "user": {
        "name": "Admin Gudang A"
      }
    }
  ]
}
```

### 4. Aktivitas Terbaru
```
GET /api/transaction-logs/recent?limit=10
```

### 5. Export CSV
```
GET /api/transaction-logs/export?days=30
```

## Akses dan Keamanan

### Hak Akses:
- **Owner**: Dapat melihat semua log transaksi milik mereka
- **Superadmin**: Dapat melihat semua log dari semua owner
- **Admin Gudang**: Tidak dapat mengakses log (hanya membuat log)
- **Sales**: Tidak dapat mengakses log

### Keamanan:
- Validasi owner scope untuk memastikan owner hanya melihat data mereka
- Log tidak dapat diedit atau dihapus
- Audit trail lengkap dengan metadata

## Testing

### 1. Unit Test
```bash
vendor/bin/phpunit tests/Unit/TransactionLogTest.php
```

**Test Cases:**
- ✅ Log approve gudang action
- ✅ Log reject action
- ✅ Log ship action  
- ✅ Log deliver action
- ✅ Get logs for transaction
- ✅ Get logs for owner
- ✅ Get activity stats
- ✅ Owner can access API
- ✅ Admin gudang cannot access API
- ✅ Owner can get stats

### 2. Manual Testing

#### Setup Data:
```bash
php artisan migrate:fresh --seed --seeder=TransactionLogTestSeeder
```

#### Login sebagai Owner:
- Email: `<EMAIL>`
- Password: `password`

#### Test API Endpoints:
```bash
# Get all logs
curl -H "Authorization: Bearer {token}" \
     http://localhost:8000/api/transaction-logs

# Get stats
curl -H "Authorization: Bearer {token}" \
     http://localhost:8000/api/transaction-logs/stats

# Get recent activities
curl -H "Authorization: Bearer {token}" \
     http://localhost:8000/api/transaction-logs/recent
```

#### Test Admin Gudang Actions:
1. Login sebagai admin gudang: `<EMAIL>` / `password`
2. Approve/reject/ship/deliver transaksi
3. Login sebagai owner untuk melihat log yang tercatat

## Contoh Penggunaan

### 1. Melihat Siapa yang Menolak Transaksi
```bash
GET /api/transaction-logs?action=reject
```

### 2. Melihat Aktivitas Admin Gudang Tertentu
```bash
GET /api/transaction-logs?user_id=5
```

### 3. Melihat Log Transaksi Bermasalah
```bash
GET /api/transaction-logs/transaction/123
```

### 4. Monitoring Performa Admin Gudang
```bash
GET /api/transaction-logs/stats?days=7
```

## Metadata yang Disimpan

Setiap log menyimpan metadata tambahan:
```json
{
  "user_name": "Admin Gudang A",
  "user_role": "admin_gudang",
  "transaction_total": 1250000,
  "mitra_name": "PT Tani Sejahtera",
  "timestamp": "2024-08-22T10:30:00Z",
  "rejection_reason": "Stok tidak mencukupi", // untuk reject
  "shipping_document": "RESI-123456", // untuk ship
  "delivery_document": "DELIVERY-789", // untuk deliver
  "items_count": 3, // untuk approve
  "payment_method": "cash" // untuk approve
}
```

## Monitoring dan Troubleshooting

### Log Files
Aktivitas logging dicatat di `storage/logs/laravel.log`:
```
[INFO] Transaction log created: transaction_id=123, user_id=5, action=approve_gudang
[ERROR] Failed to create transaction log: transaction_id=123, error=...
```

### Performance
- Index pada `transaction_id` dan `user_id` untuk query cepat
- Pagination untuk menghindari memory issues
- Lazy loading relasi untuk efisiensi

### Troubleshooting
1. **Log tidak tercatat**: Cek apakah TransactionLogService ter-inject dengan benar
2. **Owner tidak bisa akses**: Cek owner scope validation
3. **Performance lambat**: Cek index database dan query optimization

## Manfaat Sistem

1. **Transparansi**: Owner tahu siapa yang melakukan apa
2. **Akuntabilitas**: Admin gudang bertanggung jawab atas aksinya
3. **Audit**: Jejak lengkap untuk compliance dan investigasi
4. **Analisis**: Data untuk evaluasi performa tim
5. **Debugging**: Membantu troubleshoot masalah transaksi
