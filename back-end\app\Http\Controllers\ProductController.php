<?php

namespace App\Http\Controllers;

use App\Models\Product;
use App\Models\StockLog;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;

class ProductController extends Controller
{
    public function index(Request $request)
    {
        $user = $request->user();
        $query = Product::with(['category', 'unit'])->orderBy('name', 'asc');

        if ($user && $user->role !== 'superadmin') {
            $ownerScopeId = $user->role === 'owner' ? $user->id : $user->owner_id;
            if ($ownerScopeId) {
                // Produk yang bisa dilihat:
                // 1. Produk yang dibuat oleh owner atau timnya
                // 2. Produk yang dibuat oleh superadmin (tersedia untuk semua)
                $query->join('users as creator', 'products.created_by', '=', 'creator.id')
                    ->where(function ($q) use ($ownerScopeId) {
                        $q->where(function ($ownerScope) use ($ownerScopeId) {
                            // Produk dari owner scope sendiri
                            $ownerScope->where('creator.id', $ownerScopeId)
                                ->orWhere('creator.owner_id', $ownerScopeId);
                        })->orWhere('creator.role', 'superadmin'); // Produk dari superadmin
                    })
                    ->select('products.*');
            } else {
                // Fallback: produk yang dibuat user sendiri + produk dari superadmin
                $query->join('users as creator', 'products.created_by', '=', 'creator.id')
                    ->where(function ($q) use ($user) {
                        $q->where('products.created_by', $user->id)
                            ->orWhere('creator.role', 'superadmin');
                    })
                    ->select('products.*');
            }
        }

        return $query->paginate(20);
    }

    public function store(Request $request)
    {
        $data = $request->validate([
            'name' => ['required', 'string', 'max:255'],
            'category_id' => ['required', 'exists:categories,id'],
            'price' => ['required', 'integer', 'min:0'],
            'hpp' => ['required', 'integer', 'min:0'],
            'stock' => ['nullable', 'integer', 'min:0'],
            'unit_id' => ['nullable', 'exists:units,id'],
            'description' => ['nullable', 'string']
        ]);

        // Enforce category belongs to same owner scope for non-superadmin
        $user = $request->user();
        if ($user && $user->role !== 'superadmin') {
            $ownerScopeId = $user->role === 'owner' ? $user->id : $user->owner_id;
            $category = \App\Models\Category::find($data['category_id']);
            $creator = $category ? \App\Models\User::find($category->created_by) : null;
            $creatorOwnerId = $creator ? ($creator->role === 'owner' ? $creator->id : $creator->owner_id) : null;
            if (!$ownerScopeId || !$creatorOwnerId || $ownerScopeId !== $creatorOwnerId) {
                return response()->json(['message' => 'Forbidden'], 403);
            }
        }

        $data['created_by'] = $user->id ?? null;
        $product = Product::create($data);
        return response()->json($product->load(['category', 'unit']), 201);
    }

    public function update(Request $request, Product $product)
    {
        $user = $request->user();
        if ($user && $user->role !== 'superadmin') {
            $ownerScopeId = $user->role === 'owner' ? $user->id : $user->owner_id;
            $creator = \App\Models\User::find($product->created_by);
            $creatorOwnerId = $creator ? ($creator->role === 'owner' ? $creator->id : $creator->owner_id) : null;
            if (!$ownerScopeId || !$creatorOwnerId || $ownerScopeId !== $creatorOwnerId) {
                return response()->json(['message' => 'Forbidden'], 403);
            }
        }

        $data = $request->validate([
            'name' => ['required', 'string', 'max:255'],
            'category_id' => ['required', 'exists:categories,id'],
            'price' => ['required', 'integer', 'min:0'],
            'hpp' => ['required', 'integer', 'min:0'],
            'stock' => ['nullable', 'integer', 'min:0'],
            'unit_id' => ['nullable', 'exists:units,id'],
            'description' => ['nullable', 'string']
        ]);

        // Enforce new category belongs to same owner scope for non-superadmin
        $user = $request->user();
        if ($user && $user->role !== 'superadmin') {
            $ownerScopeId = $user->role === 'owner' ? $user->id : $user->owner_id;
            $category = \App\Models\Category::find($data['category_id']);
            $creator = $category ? \App\Models\User::find($category->created_by) : null;
            $creatorOwnerId = $creator ? ($creator->role === 'owner' ? $creator->id : $creator->owner_id) : null;
            if (!$ownerScopeId || !$creatorOwnerId || $ownerScopeId !== $creatorOwnerId) {
                return response()->json(['message' => 'Forbidden'], 403);
            }
        }

        $product->update($data);
        return response()->json($product->load(['category', 'unit']));
    }

    public function destroy(Request $request, Product $product)
    {
        $user = $request->user();
        if ($user && $user->role !== 'superadmin') {
            $ownerScopeId = $user->role === 'owner' ? $user->id : $user->owner_id;
            $creator = \App\Models\User::find($product->created_by);
            $creatorOwnerId = $creator ? ($creator->role === 'owner' ? $creator->id : $creator->owner_id) : null;
            if (!$ownerScopeId || !$creatorOwnerId || $ownerScopeId !== $creatorOwnerId) {
                return response()->json(['message' => 'Forbidden'], 403);
            }
        }

        $product->delete();
        return response()->json(['ok' => true]);
    }

    public function adjustStock(Request $request, Product $product)
    {
        // Authorize by owner scope
        $user = $request->user();
        if ($user && $user->role !== 'superadmin') {
            $ownerScopeId = $user->role === 'owner' ? $user->id : $user->owner_id;
            $creator = \App\Models\User::find($product->created_by);
            $creatorOwnerId = $creator ? ($creator->role === 'owner' ? $creator->id : $creator->owner_id) : null;
            if (!$ownerScopeId || !$creatorOwnerId || $ownerScopeId !== $creatorOwnerId) {
                return response()->json(['message' => 'Forbidden'], 403);
            }
        }

        $data = $request->validate([
            'type' => ['required', 'in:in,out,adjustment'],
            'quantity' => ['required', 'integer'],
            'reason' => ['nullable', 'string']
        ]);

        return DB::transaction(function () use ($product, $data, $request) {
            $prev = $product->stock;
            $new = $prev;
            if ($data['type'] === 'in') $new = $prev + max(0, $data['quantity']);
            elseif ($data['type'] === 'out') $new = $prev - max(0, $data['quantity']);
            else $new = $prev + $data['quantity'];
            if ($new < 0) $new = 0;
            $product->update(['stock' => $new]);

            StockLog::create([
                'product_id' => $product->id,
                'type' => $data['type'],
                'quantity' => $data['quantity'],
                'previous_stock' => $prev,
                'new_stock' => $new,
                'reason' => $data['reason'] ?? null,
                'created_by' => $request->user()->id ?? null,
            ]);

            return response()->json($product->refresh());
        });
    }
}
