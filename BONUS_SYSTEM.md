# Sistem Bonus untuk Owner

## Deskripsi
Sistem bonus memungkinkan owner untuk membuat program bonus berdasarkan pembelian produk tertentu. Ketika sales melakukan transaksi yang memenuhi syarat bonus, sistem akan otomatis mencatat klaim bonus untuk mitra yang bersangkutan.

## Fitur Utama

### 1. Manajemen Bonus oleh Owner
- **Nama Event**: Nama program bonus yang mudah diingat
- **Kategori Produk**: Bonus berlaku untuk kategori produk tertentu
- **Produk Spesifik**: Opsional, bonus hanya untuk produk tertentu dalam kategori
- **Jumlah Pembelian Minimum**: Syarat minimum quantity untuk mendapat bonus
- **Deskripsi Bonus**: Penjelasan bonus yang akan diterima mitra
- **Jenis Bonus**: 
  - **Periode**: Bonus berlaku dalam rentang tanggal tertentu
  - **Quota**: Bonus terbatas dengan jumlah quota tertentu

### 2. Otomati<PERSON>laim Bonus
- Sistem otomatis mengecek setiap transaksi baru
- Jika memenuhi syarat, bonus langsung diklaim untuk mitra
- Mencegah klaim ganda untuk transaksi yang sama
- Update quota otomatis untuk bonus tipe quota

### 3. Monitoring dan Laporan
- Owner dapat melihat semua bonus yang dibuat
- Daftar klaim bonus dari mitra
- Statistik penggunaan bonus
- Status bonus (aktif, tidak aktif, kedaluwarsa)

## Komponen Sistem

### 1. Database Schema

#### Tabel `bonuses`
```sql
- id: Primary key
- name: Nama event bonus
- category_id: Foreign key ke categories
- product_id: Foreign key ke products (nullable)
- minimum_quantity: Jumlah pembelian minimum
- bonus_description: Deskripsi bonus
- type: Jenis bonus (periode/quota)
- start_date: Tanggal mulai (untuk periode)
- end_date: Tanggal sampai (untuk periode)
- quota_total: Total quota (untuk quota)
- quota_used: Quota yang sudah digunakan
- status: Status bonus (active/inactive/expired)
- created_by: Foreign key ke users (owner)
```

#### Tabel `bonus_claims`
```sql
- id: Primary key
- bonus_id: Foreign key ke bonuses
- transaction_id: Foreign key ke transactions
- mitra_id: Foreign key ke mitras
- quantity_purchased: Jumlah yang dibeli
- bonus_received: Bonus yang diterima
- status: Status klaim (pending/claimed/expired)
- claimed_at: Waktu klaim
- notes: Catatan tambahan
```

### 2. Model dan Relasi
- **Bonus**: Model utama dengan relasi ke Category, Product, User
- **BonusClaim**: Model klaim dengan relasi ke Bonus, Transaction, Mitra
- **Scope dan Accessor**: Untuk filtering dan format data

### 3. Service Layer
- **BonusService**: Menangani logika bisnis bonus
  - `checkAndClaimBonuses()`: Cek dan klaim bonus untuk transaksi
  - `validateBonusData()`: Validasi data bonus
  - `getBonusStatsForOwner()`: Statistik bonus untuk owner
  - `updateExpiredBonuses()`: Update status bonus kedaluwarsa

### 4. API Endpoints

#### Manajemen Bonus
```
GET    /api/bonuses           - Daftar bonus
POST   /api/bonuses           - Buat bonus baru
GET    /api/bonuses/{id}      - Detail bonus
PUT    /api/bonuses/{id}      - Update bonus
DELETE /api/bonuses/{id}      - Hapus bonus
```

#### Klaim Bonus
```
GET    /api/bonus-claims      - Daftar klaim bonus
GET    /api/bonus-stats       - Statistik bonus
```

### 5. Frontend Component
- **BonusManagement.tsx**: Interface lengkap untuk owner
  - Form create/edit bonus dengan conditional fields
  - Tabel daftar bonus dengan status dan aksi
  - Tabel klaim bonus untuk monitoring
  - Filter dan search functionality

## Cara Kerja Sistem

### 1. Owner Membuat Bonus
1. Login sebagai owner
2. Akses menu Bonus Management
3. Klik "Tambah Bonus"
4. Isi form:
   - Nama event
   - Pilih kategori produk
   - Pilih produk (opsional)
   - Set minimum quantity
   - Tulis deskripsi bonus
   - Pilih jenis: Periode atau Quota
   - Jika Periode: set tanggal mulai dan sampai
   - Jika Quota: set jumlah quota
   - Set status aktif/tidak aktif
5. Simpan bonus

### 2. Sales Membuat Transaksi
1. Sales membuat transaksi seperti biasa
2. Sistem otomatis mengecek bonus yang berlaku
3. Jika transaksi memenuhi syarat:
   - Bonus diklaim otomatis
   - Quota berkurang (untuk tipe quota)
   - Klaim tersimpan di database
4. Response transaksi include informasi bonus yang diklaim

### 3. Owner Monitoring Bonus
1. Lihat daftar bonus dan statusnya
2. Monitor klaim bonus dari mitra
3. Lihat statistik penggunaan bonus
4. Edit atau nonaktifkan bonus jika diperlukan

## Contoh Penggunaan

### 1. Bonus Periode
```json
{
  "name": "Promo Pupuk Ramadan",
  "category_id": 1,
  "minimum_quantity": 20,
  "bonus_description": "Gratis pupuk organik 5kg",
  "type": "periode",
  "start_date": "2024-08-01",
  "end_date": "2024-08-31",
  "status": "active"
}
```

### 2. Bonus Quota
```json
{
  "name": "Bonus Beli Benih Padi",
  "category_id": 2,
  "product_id": 5,
  "minimum_quantity": 10,
  "bonus_description": "Gratis pupuk NPK 2kg",
  "type": "quota",
  "quota_total": 50,
  "status": "active"
}
```

### 3. Response Transaksi dengan Bonus
```json
{
  "id": 123,
  "total": 750000,
  "status": "pending_gudang",
  "claimed_bonuses": [
    {
      "id": 1,
      "name": "Promo Pupuk Ramadan",
      "bonus_description": "Gratis pupuk organik 5kg",
      "category": "Pupuk",
      "product": null
    }
  ]
}
```

## Validasi dan Aturan Bisnis

### 1. Validasi Bonus
- Nama event wajib diisi
- Kategori produk harus valid dan milik owner
- Produk (jika dipilih) harus dalam kategori yang dipilih
- Minimum quantity harus > 0
- Untuk periode: tanggal mulai < tanggal sampai
- Untuk quota: quota total harus > 0

### 2. Aturan Klaim
- Satu transaksi hanya bisa klaim satu bonus per bonus_id
- Bonus harus aktif dan valid (tidak kedaluwarsa/quota habis)
- Quantity pembelian harus >= minimum quantity
- Produk dalam transaksi harus sesuai kategori/produk bonus

### 3. Keamanan
- Hanya owner yang bisa CRUD bonus
- Owner hanya bisa akses bonus milik mereka
- Validasi owner scope untuk kategori dan produk
- Prevent SQL injection dan XSS

## Testing

### 1. Manual Testing
```bash
# Setup data test
php artisan db:seed --class=BonusSystemTestSeeder

# Login credentials
Owner: <EMAIL> / password
Sales: <EMAIL> / password
```

### 2. Skenario Testing

#### Test Bonus Periode:
1. Login sebagai owner
2. Buat bonus periode untuk kategori Pupuk (min 20kg)
3. Login sebagai sales
4. Buat transaksi dengan Pupuk NPK 25kg
5. Verifikasi bonus diklaim otomatis

#### Test Bonus Quota:
1. Buat bonus quota untuk Benih Padi (quota 50, min 10kg)
2. Buat beberapa transaksi hingga quota habis
3. Verifikasi bonus berhenti diklaim setelah quota habis

#### Test Validasi:
1. Coba buat bonus dengan tanggal tidak valid
2. Coba buat transaksi dengan quantity < minimum
3. Verifikasi validasi berfungsi

### 3. API Testing
```bash
# Test create bonus
curl -X POST /api/bonuses \
  -H "Authorization: Bearer {token}" \
  -H "Content-Type: application/json" \
  -d '{
    "name": "Test Bonus",
    "category_id": 1,
    "minimum_quantity": 10,
    "bonus_description": "Test bonus",
    "type": "quota",
    "quota_total": 100,
    "status": "active"
  }'

# Test get bonus claims
curl -H "Authorization: Bearer {token}" \
     /api/bonus-claims
```

## Monitoring dan Maintenance

### 1. Log Monitoring
- Bonus creation/update logs
- Bonus claim success/failure logs
- Quota usage tracking
- Error handling logs

### 2. Scheduled Tasks
```php
// Update expired bonuses (daily)
$schedule->call(function () {
    app(BonusService::class)->updateExpiredBonuses();
})->daily();
```

### 3. Performance Optimization
- Index pada foreign keys dan status
- Eager loading untuk relasi
- Pagination untuk large datasets
- Cache untuk bonus yang sering diakses

## Pengembangan Lanjutan

### 1. Fitur Tambahan
- Bonus bertingkat (semakin banyak beli, bonus semakin besar)
- Bonus kombinasi (beli produk A + B dapat bonus C)
- Notifikasi WhatsApp untuk mitra yang dapat bonus
- Dashboard analytics untuk owner

### 2. Integrasi
- Integrasi dengan sistem loyalty point
- Export data bonus untuk reporting
- API untuk aplikasi mobile mitra
- Webhook untuk sistem eksternal

Sistem bonus ini memberikan fleksibilitas tinggi bagi owner untuk membuat program promosi yang menarik sambil tetap mudah digunakan dan dipantau.
