<?php

namespace App\Http\Controllers;

use App\Models\Unit;
use Illuminate\Http\Request;

class UnitController extends Controller
{
    /**
     * Daftar satuan (hanya super admin)
     */
    public function index(Request $request)
    {
        $user = $request->user();
        
        // Hanya super admin yang bisa mengakses
        if (!$user || $user->role !== 'superadmin') {
            return response()->json(['message' => 'Forbidden'], 403);
        }

        $perPage = (int) $request->query('per_page', 20);
        $status = $request->query('status');
        $search = $request->query('search');

        $query = Unit::with('creator:id,name');

        // Filter berdasarkan status
        if ($status && in_array($status, ['active', 'inactive'])) {
            $query->where('status', $status);
        }

        // Search
        if ($search) {
            $query->search($search);
        }

        $units = $query->orderBy('name', 'asc')->paginate($perPage);

        // Tambahkan informasi penggunaan
        $units->getCollection()->transform(function ($unit) {
            $unit->product_count = $unit->product_count;
            $unit->can_be_deleted = $unit->canBeDeleted();
            return $unit;
        });

        return response()->json($units);
    }

    /**
     * Daftar satuan aktif untuk dropdown (semua role)
     */
    public function list(Request $request)
    {
        $user = $request->user();
        
        if (!$user) {
            return response()->json(['message' => 'Unauthorized'], 401);
        }

        $units = Unit::active()
            ->orderBy('name', 'asc')
            ->get(['id', 'name', 'content', 'quantity', 'base_unit']);

        return response()->json([
            'data' => $units->map(function ($unit) {
                return [
                    'id' => $unit->id,
                    'name' => $unit->name,
                    'content' => $unit->content,
                    'display_text' => $unit->display_text,
                    'conversion_info' => $unit->conversion_info
                ];
            })
        ]);
    }

    /**
     * Buat satuan baru
     */
    public function store(Request $request)
    {
        $user = $request->user();
        
        if (!$user || $user->role !== 'superadmin') {
            return response()->json(['message' => 'Forbidden'], 403);
        }

        $data = $request->validate([
            'name' => ['required', 'string', 'max:255', 'unique:units,name'],
            'content' => ['required', 'string', 'max:255'],
            'quantity' => ['required', 'integer', 'min:1'],
            'base_unit' => ['required', 'string', 'max:255'],
            'description' => ['nullable', 'string'],
            'status' => ['required', 'in:active,inactive']
        ]);

        $data['created_by'] = $user->id;

        $unit = Unit::create($data);
        
        return response()->json($unit->load('creator:id,name'), 201);
    }

    /**
     * Detail satuan
     */
    public function show(Request $request, Unit $unit)
    {
        $user = $request->user();
        
        if (!$user || $user->role !== 'superadmin') {
            return response()->json(['message' => 'Forbidden'], 403);
        }

        $unit->load(['creator:id,name']);
        $unit->product_count = $unit->product_count;
        $unit->can_be_deleted = $unit->canBeDeleted();

        return response()->json($unit);
    }

    /**
     * Update satuan
     */
    public function update(Request $request, Unit $unit)
    {
        $user = $request->user();
        
        if (!$user || $user->role !== 'superadmin') {
            return response()->json(['message' => 'Forbidden'], 403);
        }

        $data = $request->validate([
            'name' => ['required', 'string', 'max:255', 'unique:units,name,' . $unit->id],
            'content' => ['required', 'string', 'max:255'],
            'quantity' => ['required', 'integer', 'min:1'],
            'base_unit' => ['required', 'string', 'max:255'],
            'description' => ['nullable', 'string'],
            'status' => ['required', 'in:active,inactive']
        ]);

        $unit->update($data);
        
        return response()->json($unit->load('creator:id,name'));
    }

    /**
     * Hapus satuan
     */
    public function destroy(Request $request, Unit $unit)
    {
        $user = $request->user();
        
        if (!$user || $user->role !== 'superadmin') {
            return response()->json(['message' => 'Forbidden'], 403);
        }

        // Cek apakah satuan sedang digunakan
        if (!$unit->canBeDeleted()) {
            return response()->json([
                'message' => 'Cannot delete unit that is being used by products',
                'products_count' => $unit->product_count
            ], 422);
        }

        $unit->delete();
        
        return response()->json(['message' => 'Unit deleted successfully']);
    }

    /**
     * Statistik penggunaan satuan
     */
    public function stats(Request $request)
    {
        $user = $request->user();
        
        if (!$user || $user->role !== 'superadmin') {
            return response()->json(['message' => 'Forbidden'], 403);
        }

        $totalUnits = Unit::count();
        $activeUnits = Unit::active()->count();
        $inactiveUnits = Unit::inactive()->count();
        $unitsInUse = Unit::whereHas('products')->count();
        $unusedUnits = Unit::whereDoesntHave('products')->count();

        // Top 5 satuan yang paling banyak digunakan
        $mostUsedUnits = Unit::withCount('products')
            ->orderBy('products_count', 'desc')
            ->limit(5)
            ->get(['id', 'name', 'content']);

        return response()->json([
            'total_units' => $totalUnits,
            'active_units' => $activeUnits,
            'inactive_units' => $inactiveUnits,
            'units_in_use' => $unitsInUse,
            'unused_units' => $unusedUnits,
            'most_used_units' => $mostUsedUnits
        ]);
    }

    /**
     * Bulk update status satuan
     */
    public function bulkUpdateStatus(Request $request)
    {
        $user = $request->user();
        
        if (!$user || $user->role !== 'superadmin') {
            return response()->json(['message' => 'Forbidden'], 403);
        }

        $data = $request->validate([
            'unit_ids' => ['required', 'array'],
            'unit_ids.*' => ['exists:units,id'],
            'status' => ['required', 'in:active,inactive']
        ]);

        $updatedCount = Unit::whereIn('id', $data['unit_ids'])
            ->update(['status' => $data['status']]);

        return response()->json([
            'message' => "Successfully updated {$updatedCount} units",
            'updated_count' => $updatedCount
        ]);
    }

    /**
     * Export satuan ke CSV
     */
    public function export(Request $request)
    {
        $user = $request->user();
        
        if (!$user || $user->role !== 'superadmin') {
            return response()->json(['message' => 'Forbidden'], 403);
        }

        $units = Unit::with('creator:id,name')->get();

        $csvData = [];
        $csvData[] = ['ID', 'Nama Satuan', 'Isi', 'Jumlah', 'Satuan Dasar', 'Status', 'Dibuat Oleh', 'Tanggal Dibuat'];

        foreach ($units as $unit) {
            $csvData[] = [
                $unit->id,
                $unit->name,
                $unit->content,
                $unit->quantity,
                $unit->base_unit,
                $unit->status_name,
                $unit->creator->name ?? '',
                $unit->created_at->format('Y-m-d H:i:s')
            ];
        }

        $filename = 'master_satuan_' . now()->format('Y-m-d') . '.csv';
        
        $headers = [
            'Content-Type' => 'text/csv',
            'Content-Disposition' => 'attachment; filename="' . $filename . '"',
        ];

        $callback = function() use ($csvData) {
            $file = fopen('php://output', 'w');
            foreach ($csvData as $row) {
                fputcsv($file, $row);
            }
            fclose($file);
        };

        return response()->stream($callback, 200, $headers);
    }
}
