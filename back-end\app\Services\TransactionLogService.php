<?php

namespace App\Services;

use App\Models\Transaction;
use App\Models\TransactionLog;
use App\Models\User;
use Illuminate\Support\Facades\Log;

class TransactionLogService
{
    /**
     * Log aksi approve gudang
     */
    public function logApproveGudang(Transaction $transaction, User $user, array $metadata = []): void
    {
        $this->createLog(
            transaction: $transaction,
            user: $user,
            action: 'approve_gudang',
            previousStatus: 'pending_gudang',
            newStatus: 'approved',
            notes: 'Transaksi disetujui oleh admin gudang',
            metadata: $metadata
        );
    }

    /**
     * Log aksi reject
     */
    public function logReject(Transaction $transaction, User $user, string $rejectionReason, array $metadata = []): void
    {
        $this->createLog(
            transaction: $transaction,
            user: $user,
            action: 'reject',
            previousStatus: $transaction->getOriginal('status') ?? 'pending_gudang',
            newStatus: 'rejected',
            notes: "Transaksi ditolak. Alasan: {$rejectionReason}",
            metadata: array_merge($metadata, ['rejection_reason' => $rejectionReason])
        );
    }

    /**
     * Log aksi ship (pengiriman)
     */
    public function logShip(Transaction $transaction, User $user, ?string $shippingDocument = null, array $metadata = []): void
    {
        $notes = 'Barang telah dikirim';
        if ($shippingDocument) {
            $notes .= ". Dokumen pengiriman: {$shippingDocument}";
        }

        $this->createLog(
            transaction: $transaction,
            user: $user,
            action: 'ship',
            previousStatus: 'approved',
            newStatus: 'shipped',
            notes: $notes,
            metadata: array_merge($metadata, [
                'shipping_document' => $shippingDocument
            ])
        );
    }

    /**
     * Log aksi deliver (selesai)
     */
    public function logDeliver(Transaction $transaction, User $user, ?string $deliveryDocument = null, array $metadata = []): void
    {
        $notes = 'Pengiriman telah selesai';
        if ($deliveryDocument) {
            $notes .= ". Dokumen pengiriman: {$deliveryDocument}";
        }

        $this->createLog(
            transaction: $transaction,
            user: $user,
            action: 'deliver',
            previousStatus: 'shipped',
            newStatus: 'delivered',
            notes: $notes,
            metadata: array_merge($metadata, [
                'delivery_document' => $deliveryDocument
            ])
        );
    }

    /**
     * Membuat log entry
     */
    private function createLog(
        Transaction $transaction,
        User $user,
        string $action,
        string $previousStatus,
        string $newStatus,
        string $notes,
        array $metadata = []
    ): void {
        try {
            TransactionLog::create([
                'transaction_id' => $transaction->id,
                'user_id' => $user->id,
                'action' => $action,
                'previous_status' => $previousStatus,
                'new_status' => $newStatus,
                'notes' => $notes,
                'metadata' => array_merge($metadata, [
                    'user_name' => $user->name,
                    'user_role' => $user->role,
                    'transaction_total' => $transaction->total,
                    'mitra_name' => $transaction->mitra?->name,
                    'timestamp' => now()->toISOString()
                ])
            ]);

            Log::info("Transaction log created", [
                'transaction_id' => $transaction->id,
                'user_id' => $user->id,
                'action' => $action,
                'status_change' => "{$previousStatus} -> {$newStatus}"
            ]);

        } catch (\Throwable $e) {
            Log::error("Failed to create transaction log", [
                'transaction_id' => $transaction->id,
                'user_id' => $user->id,
                'action' => $action,
                'error' => $e->getMessage()
            ]);
        }
    }

    /**
     * Mendapatkan log untuk transaksi tertentu
     */
    public function getLogsForTransaction(int $transactionId): \Illuminate\Database\Eloquent\Collection
    {
        return TransactionLog::with(['user:id,name,role'])
            ->forTransaction($transactionId)
            ->orderBy('created_at', 'desc')
            ->get();
    }

    /**
     * Mendapatkan log untuk owner tertentu
     */
    public function getLogsForOwner(int $ownerId, int $limit = 50): \Illuminate\Database\Eloquent\Collection
    {
        return TransactionLog::with(['user:id,name,role', 'transaction:id,mitra_id,total'])
            ->byOwnerScope($ownerId)
            ->orderBy('created_at', 'desc')
            ->limit($limit)
            ->get();
    }

    /**
     * Mendapatkan statistik aktivitas admin gudang untuk owner
     */
    public function getActivityStats(int $ownerId, int $days = 30): array
    {
        $startDate = now()->subDays($days);

        $stats = TransactionLog::byOwnerScope($ownerId)
            ->where('created_at', '>=', $startDate)
            ->selectRaw('action, COUNT(*) as count')
            ->groupBy('action')
            ->pluck('count', 'action')
            ->toArray();

        return [
            'approve_gudang' => $stats['approve_gudang'] ?? 0,
            'reject' => $stats['reject'] ?? 0,
            'ship' => $stats['ship'] ?? 0,
            'deliver' => $stats['deliver'] ?? 0,
            'total' => array_sum($stats),
            'period_days' => $days
        ];
    }

    /**
     * Mendapatkan admin gudang paling aktif untuk owner
     */
    public function getMostActiveAdmins(int $ownerId, int $days = 30, int $limit = 5): \Illuminate\Database\Eloquent\Collection
    {
        $startDate = now()->subDays($days);

        return TransactionLog::with(['user:id,name,role'])
            ->byOwnerScope($ownerId)
            ->where('created_at', '>=', $startDate)
            ->selectRaw('user_id, COUNT(*) as activity_count')
            ->groupBy('user_id')
            ->orderBy('activity_count', 'desc')
            ->limit($limit)
            ->get();
    }
}
