<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('products', function (Blueprint $table) {
            // Tambah kolom unit_id sebagai foreign key
            $table->foreignId('unit_id')->nullable()->after('stock')->constrained('units')->nullOnDelete();
            
            // Rename kolom unit lama menjadi unit_legacy untuk backup
            $table->renameColumn('unit', 'unit_legacy');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('products', function (Blueprint $table) {
            // Drop foreign key dan kolom unit_id
            $table->dropForeign(['unit_id']);
            $table->dropColumn('unit_id');
            
            // Rename kembali unit_legacy menjadi unit
            $table->renameColumn('unit_legacy', 'unit');
        });
    }
};
