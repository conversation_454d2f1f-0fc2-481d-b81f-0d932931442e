<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class BonusClaim extends Model
{
    use HasFactory;

    protected $fillable = [
        'bonus_id',
        'transaction_id',
        'mitra_id',
        'quantity_purchased',
        'bonus_received',
        'status',
        'claimed_at',
        'notes'
    ];

    protected $casts = [
        'quantity_purchased' => 'integer',
        'claimed_at' => 'datetime',
    ];

    /**
     * Relasi ke Bonus
     */
    public function bonus()
    {
        return $this->belongsTo(Bonus::class);
    }

    /**
     * Relasi ke Transaction
     */
    public function transaction()
    {
        return $this->belongsTo(Transaction::class);
    }

    /**
     * Relasi ke Mitra
     */
    public function mitra()
    {
        return $this->belongsTo(Mitra::class);
    }

    /**
     * Scope untuk klaim berdasarkan owner
     */
    public function scopeByOwner($query, $ownerId)
    {
        return $query->whereHas('bonus.creator', function ($q) use ($ownerId) {
            $q->where('id', $ownerId)->orWhere('owner_id', $ownerId);
        });
    }

    /**
     * Scope untuk klaim berdasarkan mitra
     */
    public function scopeByMitra($query, $mitraId)
    {
        return $query->where('mitra_id', $mitraId);
    }

    /**
     * Scope untuk klaim berdasarkan status
     */
    public function scopeByStatus($query, $status)
    {
        return $query->where('status', $status);
    }

    /**
     * Scope untuk klaim yang pending
     */
    public function scopePending($query)
    {
        return $query->where('status', 'pending');
    }

    /**
     * Scope untuk klaim yang sudah diklaim
     */
    public function scopeClaimed($query)
    {
        return $query->where('status', 'claimed');
    }

    /**
     * Mark bonus sebagai claimed
     */
    public function markAsClaimed($notes = null): void
    {
        $this->update([
            'status' => 'claimed',
            'claimed_at' => now(),
            'notes' => $notes
        ]);
    }

    /**
     * Mark bonus sebagai expired
     */
    public function markAsExpired($notes = null): void
    {
        $this->update([
            'status' => 'expired',
            'notes' => $notes
        ]);
    }

    /**
     * Accessor untuk status yang user-friendly
     */
    public function getStatusNameAttribute(): string
    {
        $statusNames = [
            'pending' => 'Menunggu Klaim',
            'claimed' => 'Sudah Diklaim',
            'expired' => 'Kedaluwarsa'
        ];

        return $statusNames[$this->status] ?? $this->status;
    }

    /**
     * Accessor untuk informasi lengkap bonus
     */
    public function getBonusInfoAttribute(): array
    {
        return [
            'bonus_name' => $this->bonus->name,
            'bonus_description' => $this->bonus_received,
            'minimum_quantity' => $this->bonus->minimum_quantity,
            'quantity_purchased' => $this->quantity_purchased,
            'category' => $this->bonus->category->name,
            'product' => $this->bonus->product ? $this->bonus->product->name : null,
        ];
    }
}
