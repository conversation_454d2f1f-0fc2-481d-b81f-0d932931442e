<?php

namespace Database\Seeders;

use App\Models\User;
use App\Models\Unit;
use App\Models\Category;
use App\Models\Product;
use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\Hash;

class UnitSystemTestSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Buat Super Admin
        $superAdmin = User::firstOrCreate(
            ['email' => '<EMAIL>'],
            [
                'name' => 'Super Admin',
                'phone' => '628123456789',
                'role' => 'superadmin',
                'password' => Hash::make('password'),
            ]
        );

        // Buat Owner
        $owner = User::firstOrCreate(
            ['email' => '<EMAIL>'],
            [
                'name' => 'Owner Test',
                'phone' => '628111000001',
                'role' => 'owner',
                'password' => Hash::make('password'),
            ]
        );

        // Buat Master Satuan
        $unitKg = Unit::firstOrCreate(
            ['name' => 'kg'],
            [
                'content' => '1 kilogram',
                'quantity' => 1,
                'base_unit' => 'kg',
                'description' => 'Satuan dasar kilogram',
                'status' => 'active',
                'created_by' => $superAdmin->id,
            ]
        );

        $unitDus = Unit::firstOrCreate(
            ['name' => 'dus'],
            [
                'content' => '12 botol',
                'quantity' => 12,
                'base_unit' => 'botol',
                'description' => 'Dus berisi 12 botol',
                'status' => 'active',
                'created_by' => $superAdmin->id,
            ]
        );

        $unitBox = Unit::firstOrCreate(
            ['name' => 'box'],
            [
                'content' => '6 sachet',
                'quantity' => 6,
                'base_unit' => 'sachet',
                'description' => 'Box berisi 6 sachet',
                'status' => 'active',
                'created_by' => $superAdmin->id,
            ]
        );

        $unitLiter = Unit::firstOrCreate(
            ['name' => 'liter'],
            [
                'content' => '1 liter',
                'quantity' => 1,
                'base_unit' => 'liter',
                'description' => 'Satuan dasar liter',
                'status' => 'active',
                'created_by' => $superAdmin->id,
            ]
        );

        $unitJerigen = Unit::firstOrCreate(
            ['name' => 'jerigen'],
            [
                'content' => '20 liter',
                'quantity' => 20,
                'base_unit' => 'liter',
                'description' => 'Jerigen berisi 20 liter',
                'status' => 'active',
                'created_by' => $superAdmin->id,
            ]
        );

        $unitPcs = Unit::firstOrCreate(
            ['name' => 'pcs'],
            [
                'content' => '1 piece',
                'quantity' => 1,
                'base_unit' => 'pcs',
                'description' => 'Satuan dasar piece',
                'status' => 'active',
                'created_by' => $superAdmin->id,
            ]
        );

        // Buat Kategori
        $categoryPupuk = Category::create([
            'name' => 'Pupuk',
            'description' => 'Kategori untuk produk pupuk',
            'created_by' => $owner->id,
        ]);

        $categoryPestisida = Category::create([
            'name' => 'Pestisida',
            'description' => 'Kategori untuk produk pestisida',
            'created_by' => $owner->id,
        ]);

        $categoryBenih = Category::create([
            'name' => 'Benih',
            'description' => 'Kategori untuk produk benih',
            'created_by' => $owner->id,
        ]);

        // Buat Produk dengan satuan dinamis
        Product::create([
            'name' => 'Pupuk NPK 16-16-16',
            'category_id' => $categoryPupuk->id,
            'price' => 125000,
            'hpp' => 100000,
            'stock' => 200,
            'unit_id' => $unitKg->id,
            'description' => 'Pupuk NPK berkualitas tinggi dijual per kilogram',
            'created_by' => $owner->id,
        ]);

        Product::create([
            'name' => 'Pestisida Organik Premium',
            'category_id' => $categoryPestisida->id,
            'price' => 150000,
            'hpp' => 120000,
            'stock' => 50,
            'unit_id' => $unitLiter->id,
            'description' => 'Pestisida organik ramah lingkungan dijual per liter',
            'created_by' => $owner->id,
        ]);

        Product::create([
            'name' => 'Pestisida Cair Jerigen',
            'category_id' => $categoryPestisida->id,
            'price' => 2800000,
            'hpp' => 2400000,
            'stock' => 10,
            'unit_id' => $unitJerigen->id,
            'description' => 'Pestisida cair dalam kemasan jerigen 20 liter',
            'created_by' => $owner->id,
        ]);

        Product::create([
            'name' => 'Benih Padi Unggul',
            'category_id' => $categoryBenih->id,
            'price' => 45000,
            'hpp' => 35000,
            'stock' => 100,
            'unit_id' => $unitKg->id,
            'description' => 'Benih padi varietas unggul dijual per kilogram',
            'created_by' => $owner->id,
        ]);

        Product::create([
            'name' => 'Pupuk Organik Kemasan Dus',
            'category_id' => $categoryPupuk->id,
            'price' => 480000,
            'hpp' => 400000,
            'stock' => 25,
            'unit_id' => $unitDus->id,
            'description' => 'Pupuk organik dalam kemasan dus berisi 12 botol',
            'created_by' => $owner->id,
        ]);

        Product::create([
            'name' => 'Pupuk Tablet Box',
            'category_id' => $categoryPupuk->id,
            'price' => 75000,
            'hpp' => 60000,
            'stock' => 80,
            'unit_id' => $unitBox->id,
            'description' => 'Pupuk tablet dalam box berisi 6 sachet',
            'created_by' => $owner->id,
        ]);

        Product::create([
            'name' => 'Alat Semprot Manual',
            'category_id' => $categoryPestisida->id,
            'price' => 250000,
            'hpp' => 200000,
            'stock' => 15,
            'unit_id' => $unitPcs->id,
            'description' => 'Alat semprot manual dijual per piece',
            'created_by' => $owner->id,
        ]);

        // Produk dengan unit legacy (untuk backward compatibility)
        Product::create([
            'name' => 'Produk Legacy',
            'category_id' => $categoryPupuk->id,
            'price' => 50000,
            'hpp' => 40000,
            'stock' => 30,
            'unit_id' => null,
            'unit_legacy' => 'kg',
            'description' => 'Produk dengan satuan legacy untuk testing backward compatibility',
            'created_by' => $owner->id,
        ]);

        $this->command->info('Test data untuk sistem master satuan berhasil dibuat!');
        $this->command->info('');
        $this->command->info('Login credentials:');
        $this->command->info('Super Admin: <EMAIL> / password');
        $this->command->info('Owner: <EMAIL> / password');
        $this->command->info('');
        $this->command->info('Master Satuan yang dibuat:');
        $this->command->info('1. kg (1 kilogram) - Aktif');
        $this->command->info('2. dus (12 botol) - Aktif');
        $this->command->info('3. karton (24 pcs) - Aktif');
        $this->command->info('4. box (6 sachet) - Aktif');
        $this->command->info('5. liter (1 liter) - Aktif');
        $this->command->info('6. jerigen (20 liter) - Aktif');
        $this->command->info('7. pcs (1 piece) - Aktif');
        $this->command->info('8. sak (50 kg) - Tidak Aktif');
        $this->command->info('');
        $this->command->info('Produk dengan satuan dinamis:');
        $this->command->info('- Pupuk NPK (kg)');
        $this->command->info('- Pestisida Organik (liter)');
        $this->command->info('- Pestisida Jerigen (jerigen)');
        $this->command->info('- Benih Padi (kg)');
        $this->command->info('- Pupuk Organik (dus)');
        $this->command->info('- Pupuk Tablet (box)');
        $this->command->info('- Alat Semprot (pcs)');
        $this->command->info('- Produk Legacy (unit_legacy)');
        $this->command->info('');
        $this->command->info('Cara testing:');
        $this->command->info('1. Login sebagai super admin untuk mengelola master satuan');
        $this->command->info('2. Login sebagai owner untuk membuat produk dengan satuan dinamis');
        $this->command->info('3. Cek bahwa dropdown satuan di form produk menggunakan data dari master satuan');
    }
}
