# Sistem Master Satuan

## Deskripsi
Sistem master satuan memungkinkan super admin untuk mengelola satuan produk secara terpusat. Setiap satuan memiliki nama, isi, dan konversi ke satuan dasar. Sistem produk kemudian menggunakan satuan dinamis dari master ini, bukan lagi satuan statis.

## Fitur Utama

### 1. Master <PERSON>h Super Admin
- **<PERSON><PERSON>**: <PERSON><PERSON> satuan (dus, karton, box, dll)
- **Is<PERSON>**: Deskripsi isi (12 botol, 24 pcs, dll)
- **<PERSON><PERSON><PERSON> (Angka)**: <PERSON><PERSON> numerik untuk konversi (12, 24, dll)
- **<PERSON><PERSON><PERSON>ar**: Satuan terkecil (botol, pcs, kg, liter, dll)
- **Deskripsi**: Penjelasan tambahan tentang satuan
- **Status**: Aktif/Tidak Aktif

### 2. Sistem Konversi
- Setiap satuan memiliki konversi ke satuan dasar
- Contoh: 1 dus = 12 botol, 1 jerigen = 20 liter
- Memudahkan perhitungan stok dan harga

### 3. Integrasi dengan Produk
- Form produk menggunakan dropdown satuan dinamis
- Hanya satuan aktif yang muncul di dropdown
- Backward compatibility dengan produk lama

## Komponen Sistem

### 1. Database Schema

#### Tabel `units`
```sql
- id: Primary key
- name: Nama satuan (unique)
- content: Isi dalam satuan (text)
- quantity: Jumlah dalam angka (integer)
- base_unit: Satuan dasar (string)
- description: Deskripsi tambahan (nullable)
- status: Status (active/inactive)
- created_by: Foreign key ke users (super admin)
```

#### Modifikasi Tabel `products`
```sql
- unit_id: Foreign key ke units (nullable)
- unit_legacy: Rename dari unit lama (untuk backward compatibility)
```

### 2. Model dan Relasi
- **Unit**: Model utama dengan relasi ke User dan Product
- **Product**: Ditambah relasi ke Unit
- **Accessor**: Untuk backward compatibility dan display text

### 3. API Endpoints

#### Master Satuan (Super Admin Only)
```
GET    /api/units              - Daftar satuan (dengan pagination, filter, search)
POST   /api/units              - Buat satuan baru
GET    /api/units/{id}         - Detail satuan
PUT    /api/units/{id}         - Update satuan
DELETE /api/units/{id}         - Hapus satuan (jika tidak digunakan)
GET    /api/units-stats        - Statistik penggunaan satuan
POST   /api/units/bulk-status  - Update status satuan secara bulk
GET    /api/units/export       - Export satuan ke CSV
```

#### Dropdown Satuan (All Roles)
```
GET    /api/units/list         - Daftar satuan aktif untuk dropdown
```

### 4. Frontend Components
- **UnitManagement.tsx**: Interface lengkap untuk super admin
  - CRUD satuan dengan form validation
  - Bulk operations (aktifkan/nonaktifkan)
  - Statistik dan monitoring penggunaan
  - Export data ke CSV
  - Search dan filter

## Contoh Data Master Satuan

### 1. Satuan Dasar
```json
{
  "name": "kg",
  "content": "1 kilogram",
  "quantity": 1,
  "base_unit": "kg",
  "description": "Satuan dasar kilogram"
}
```

### 2. Satuan Kemasan
```json
{
  "name": "dus",
  "content": "12 botol",
  "quantity": 12,
  "base_unit": "botol",
  "description": "Dus berisi 12 botol"
}
```

### 3. Satuan Besar
```json
{
  "name": "jerigen",
  "content": "20 liter",
  "quantity": 20,
  "base_unit": "liter",
  "description": "Jerigen berisi 20 liter"
}
```

## Cara Kerja Sistem

### 1. Super Admin Mengelola Master Satuan
1. Login sebagai super admin
2. Akses menu Master Satuan
3. Tambah/edit/hapus satuan sesuai kebutuhan
4. Set status aktif/tidak aktif
5. Monitor penggunaan satuan

### 2. Owner/User Membuat Produk
1. Buka form tambah/edit produk
2. Pilih satuan dari dropdown dinamis
3. Dropdown hanya menampilkan satuan aktif
4. Sistem otomatis menggunakan unit_id

### 3. Sistem Konversi Otomatis
- Produk dengan satuan "dus" otomatis tahu = 12 botol
- Memudahkan perhitungan stok dalam satuan dasar
- Konsistensi data di seluruh sistem

## Validasi dan Aturan Bisnis

### 1. Validasi Master Satuan
- Nama satuan harus unique
- Quantity harus > 0
- Hanya super admin yang bisa CRUD
- Satuan tidak bisa dihapus jika sedang digunakan produk

### 2. Backward Compatibility
- Produk lama tetap menggunakan unit_legacy
- Accessor getUnitNameAttribute() untuk kompatibilitas
- Migrasi bertahap dari unit lama ke unit baru

### 3. Keamanan
- Endpoint master satuan hanya untuk super admin
- Endpoint list satuan untuk semua role (dropdown)
- Validasi foreign key constraint

## Testing

### 1. Manual Testing
```bash
# Setup data test
php artisan db:seed --class=UnitSystemTestSeeder

# Login credentials
Super Admin: <EMAIL> / password
Owner: <EMAIL> / password
```

### 2. Skenario Testing

#### Test Master Satuan:
1. Login sebagai super admin
2. Buat satuan baru (misal: "pack" = "6 kaleng")
3. Edit satuan yang ada
4. Coba hapus satuan yang sedang digunakan (harus error)
5. Nonaktifkan satuan dan cek tidak muncul di dropdown

#### Test Produk dengan Satuan Dinamis:
1. Login sebagai owner
2. Buat produk baru
3. Pilih satuan dari dropdown
4. Verifikasi data tersimpan dengan unit_id
5. Cek backward compatibility dengan produk lama

#### Test API:
```bash
# Test get units list
curl -H "Authorization: Bearer {token}" /api/units/list

# Test create unit (super admin only)
curl -X POST /api/units \
  -H "Authorization: Bearer {token}" \
  -H "Content-Type: application/json" \
  -d '{
    "name": "pack",
    "content": "6 kaleng",
    "quantity": 6,
    "base_unit": "kaleng",
    "description": "Pack berisi 6 kaleng",
    "status": "active"
  }'
```

## Contoh Implementasi

### 1. Response API Units List
```json
{
  "data": [
    {
      "id": 1,
      "name": "kg",
      "content": "1 kilogram",
      "display_text": "kg - 1 kilogram",
      "conversion_info": "1 kg = 1 kg"
    },
    {
      "id": 2,
      "name": "dus",
      "content": "12 botol",
      "display_text": "dus - 12 botol",
      "conversion_info": "1 dus = 12 botol"
    }
  ]
}
```

### 2. Response API Product dengan Unit
```json
{
  "id": 1,
  "name": "Pupuk NPK Premium",
  "price": 125000,
  "stock": 200,
  "unit_id": 1,
  "unit": {
    "id": 1,
    "name": "kg",
    "content": "1 kilogram",
    "display_text": "kg - 1 kilogram"
  },
  "unit_name": "kg",
  "unit_display": "kg - 1 kilogram"
}
```

### 3. Form Produk dengan Dropdown Dinamis
```tsx
// Fetch units untuk dropdown
const [units, setUnits] = useState([]);

useEffect(() => {
  fetchUnits();
}, []);

const fetchUnits = async () => {
  const response = await fetch('/api/units/list');
  const data = await response.json();
  setUnits(data.data);
};

// Render dropdown
<select name="unit_id">
  <option value="">Pilih Satuan</option>
  {units.map(unit => (
    <option key={unit.id} value={unit.id}>
      {unit.display_text}
    </option>
  ))}
</select>
```

## Monitoring dan Maintenance

### 1. Statistik Penggunaan
- Total satuan aktif/tidak aktif
- Satuan yang paling banyak digunakan
- Satuan yang tidak digunakan (kandidat hapus)

### 2. Bulk Operations
- Aktifkan/nonaktifkan multiple satuan
- Export data untuk backup
- Import data untuk restore

### 3. Performance Optimization
- Index pada foreign keys
- Eager loading relasi unit di product
- Cache units list untuk dropdown

## Pengembangan Lanjutan

### 1. Fitur Tambahan
- Konversi otomatis antar satuan
- Satuan dengan multiple level (dus > box > sachet)
- Template satuan untuk kategori tertentu
- History perubahan master satuan

### 2. Integrasi
- Sinkronisasi dengan sistem inventory
- API untuk aplikasi mobile
- Webhook untuk perubahan master satuan
- Integration dengan sistem ERP

### 3. Validasi Lanjutan
- Validasi konversi satuan
- Warning jika mengubah satuan yang banyak digunakan
- Approval workflow untuk perubahan master satuan

Sistem master satuan ini memberikan fleksibilitas dan konsistensi dalam pengelolaan satuan produk, sambil tetap mempertahankan backward compatibility dengan sistem yang sudah ada.
