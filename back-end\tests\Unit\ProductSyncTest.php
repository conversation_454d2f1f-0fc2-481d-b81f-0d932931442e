<?php

namespace Tests\Unit;

use Tests\TestCase;
use App\Models\Product;
use App\Models\User;
use App\Models\Category;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Support\Facades\Hash;

class ProductSyncTest extends TestCase
{
    use RefreshDatabase;

    protected function setUp(): void
    {
        parent::setUp();
    }

    /** @test */
    public function admin_gudang_can_see_products_created_by_superadmin()
    {
        // Arrange: Buat super admin
        $superAdmin = User::create([
            'name' => 'Super Admin',
            'email' => '<EMAIL>',
            'phone' => '628123456789',
            'role' => 'superadmin',
            'password' => Hash::make('password'),
        ]);

        // Buat owner
        $owner = User::create([
            'name' => 'Owner',
            'email' => '<EMAIL>',
            'phone' => '628111111111',
            'role' => 'owner',
            'password' => Hash::make('password'),
        ]);

        // Buat admin gudang
        $adminGudang = User::create([
            'name' => 'Admin Gudang',
            'email' => '<EMAIL>',
            'phone' => '628222222222',
            'role' => 'admin_gudang',
            'owner_id' => $owner->id,
            'password' => Hash::make('password'),
        ]);

        // Buat kategori oleh super admin
        $category = Category::create([
            'name' => 'Test Category',
            'description' => 'Test category description',
            'created_by' => $superAdmin->id,
        ]);

        // Buat produk oleh super admin
        $superAdminProduct = Product::create([
            'name' => 'Super Admin Product',
            'category_id' => $category->id,
            'price' => 50000,
            'hpp' => 40000,
            'stock' => 100,
            'unit' => 'kg',
            'description' => 'Product created by super admin',
            'created_by' => $superAdmin->id,
        ]);

        // Buat produk oleh owner
        $ownerProduct = Product::create([
            'name' => 'Owner Product',
            'category_id' => $category->id,
            'price' => 30000,
            'hpp' => 25000,
            'stock' => 50,
            'unit' => 'pcs',
            'description' => 'Product created by owner',
            'created_by' => $owner->id,
        ]);

        // Act: Ambil produk sebagai admin gudang
        $response = $this->actingAs($adminGudang, 'sanctum')
            ->getJson('/api/products');

        // Assert: Admin gudang bisa melihat kedua produk
        $response->assertStatus(200);
        $products = $response->json('data');
        
        $this->assertCount(2, $products);
        
        $productNames = collect($products)->pluck('name')->toArray();
        $this->assertContains('Super Admin Product', $productNames);
        $this->assertContains('Owner Product', $productNames);
    }

    /** @test */
    public function admin_gudang_cannot_see_products_from_other_owners()
    {
        // Arrange: Buat 2 owner
        $owner1 = User::create([
            'name' => 'Owner 1',
            'email' => '<EMAIL>',
            'phone' => '628111111111',
            'role' => 'owner',
            'password' => Hash::make('password'),
        ]);

        $owner2 = User::create([
            'name' => 'Owner 2',
            'email' => '<EMAIL>',
            'phone' => '628111111112',
            'role' => 'owner',
            'password' => Hash::make('password'),
        ]);

        // Buat admin gudang untuk owner 1
        $adminGudang1 = User::create([
            'name' => 'Admin Gudang 1',
            'email' => '<EMAIL>',
            'phone' => '628222222221',
            'role' => 'admin_gudang',
            'owner_id' => $owner1->id,
            'password' => Hash::make('password'),
        ]);

        // Buat kategori
        $category = Category::create([
            'name' => 'Test Category',
            'description' => 'Test category description',
            'created_by' => $owner1->id,
        ]);

        // Buat produk oleh owner 1
        $owner1Product = Product::create([
            'name' => 'Owner 1 Product',
            'category_id' => $category->id,
            'price' => 30000,
            'hpp' => 25000,
            'stock' => 50,
            'unit' => 'pcs',
            'description' => 'Product created by owner 1',
            'created_by' => $owner1->id,
        ]);

        // Buat produk oleh owner 2
        $owner2Product = Product::create([
            'name' => 'Owner 2 Product',
            'category_id' => $category->id,
            'price' => 40000,
            'hpp' => 35000,
            'stock' => 30,
            'unit' => 'kg',
            'description' => 'Product created by owner 2',
            'created_by' => $owner2->id,
        ]);

        // Act: Ambil produk sebagai admin gudang owner 1
        $response = $this->actingAs($adminGudang1, 'sanctum')
            ->getJson('/api/products');

        // Assert: Admin gudang 1 hanya bisa melihat produk owner 1
        $response->assertStatus(200);
        $products = $response->json('data');
        
        $this->assertCount(1, $products);
        $this->assertEquals('Owner 1 Product', $products[0]['name']);
    }

    /** @test */
    public function superadmin_can_see_all_products()
    {
        // Arrange: Buat super admin dan owner
        $superAdmin = User::create([
            'name' => 'Super Admin',
            'email' => '<EMAIL>',
            'phone' => '628123456789',
            'role' => 'superadmin',
            'password' => Hash::make('password'),
        ]);

        $owner = User::create([
            'name' => 'Owner',
            'email' => '<EMAIL>',
            'phone' => '628111111111',
            'role' => 'owner',
            'password' => Hash::make('password'),
        ]);

        // Buat kategori
        $category = Category::create([
            'name' => 'Test Category',
            'description' => 'Test category description',
            'created_by' => $superAdmin->id,
        ]);

        // Buat produk oleh super admin
        $superAdminProduct = Product::create([
            'name' => 'Super Admin Product',
            'category_id' => $category->id,
            'price' => 50000,
            'hpp' => 40000,
            'stock' => 100,
            'unit' => 'kg',
            'description' => 'Product created by super admin',
            'created_by' => $superAdmin->id,
        ]);

        // Buat produk oleh owner
        $ownerProduct = Product::create([
            'name' => 'Owner Product',
            'category_id' => $category->id,
            'price' => 30000,
            'hpp' => 25000,
            'stock' => 50,
            'unit' => 'pcs',
            'description' => 'Product created by owner',
            'created_by' => $owner->id,
        ]);

        // Act: Ambil produk sebagai super admin
        $response = $this->actingAs($superAdmin, 'sanctum')
            ->getJson('/api/products');

        // Assert: Super admin bisa melihat semua produk
        $response->assertStatus(200);
        $products = $response->json('data');
        
        $this->assertCount(2, $products);
        
        $productNames = collect($products)->pluck('name')->toArray();
        $this->assertContains('Super Admin Product', $productNames);
        $this->assertContains('Owner Product', $productNames);
    }
}
