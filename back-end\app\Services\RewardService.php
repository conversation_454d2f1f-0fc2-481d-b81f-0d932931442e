<?php

namespace App\Services;

use App\Models\Reward;
use App\Models\RewardProgress;
use App\Models\Transaction;
use App\Models\TransactionItem;
use App\Models\Mitra;
use Carbon\Carbon;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;

class RewardService
{
    /**
     * Cek dan update progress reward untuk transaksi
     */
    public function checkAndUpdateRewardProgress(Transaction $transaction): array
    {
        $updatedRewards = [];

        try {
            DB::beginTransaction();

            // Ambil semua reward yang sedang aktif dan berlaku
            $activeRewards = Reward::running()
                ->with(['category', 'product'])
                ->get();

            foreach ($activeRewards as $reward) {
                $eligibleItems = $this->getEligibleItems($transaction, $reward);
                
                if ($eligibleItems->isNotEmpty()) {
                    $totalQuantity = $eligibleItems->sum('quantity');
                    
                    $progress = $this->updateOrCreateProgress(
                        $reward,
                        $transaction->mitra_id,
                        $totalQuantity,
                        $transaction
                    );
                    
                    if ($progress) {
                        $updatedRewards[] = [
                            'reward' => $reward,
                            'progress' => $progress,
                            'added_quantity' => $totalQuantity
                        ];
                    }
                }
            }

            DB::commit();
            
            Log::info('Reward progress updated', [
                'transaction_id' => $transaction->id,
                'mitra_id' => $transaction->mitra_id,
                'updated_rewards_count' => count($updatedRewards)
            ]);

        } catch (\Exception $e) {
            DB::rollBack();
            Log::error('Error updating reward progress', [
                'transaction_id' => $transaction->id,
                'error' => $e->getMessage()
            ]);
        }

        return $updatedRewards;
    }

    /**
     * Get item transaksi yang eligible untuk reward
     */
    private function getEligibleItems(Transaction $transaction, Reward $reward)
    {
        return $transaction->items->filter(function ($item) use ($reward) {
            return $reward->isEligibleProduct($item->product_id);
        });
    }

    /**
     * Update atau buat progress reward untuk mitra
     */
    private function updateOrCreateProgress(Reward $reward, int $mitraId, int $quantity, Transaction $transaction): ?RewardProgress
    {
        $progress = RewardProgress::firstOrCreate(
            [
                'reward_id' => $reward->id,
                'mitra_id' => $mitraId
            ],
            [
                'target_quantity' => $reward->target_quantity,
                'current_quantity' => 0,
                'progress_percentage' => 0,
                'status' => 'in_progress',
                'purchase_history' => []
            ]
        );

        // Update progress dengan quantity baru
        $transactionData = [
            'transaction_id' => $transaction->id,
            'sales_id' => $transaction->sales_id,
            'total_amount' => $transaction->total
        ];

        $progress->updateProgress($quantity, $transactionData);

        return $progress;
    }

    /**
     * Get leaderboard untuk reward tertentu
     */
    public function getRewardLeaderboard(int $rewardId, int $limit = 10): array
    {
        $reward = Reward::with(['category', 'product'])->find($rewardId);
        
        if (!$reward) {
            return [];
        }

        $progresses = RewardProgress::where('reward_id', $rewardId)
            ->with(['mitra'])
            ->orderBy('current_quantity', 'desc')
            ->orderBy('progress_percentage', 'desc')
            ->orderBy('updated_at', 'asc') // Yang lebih dulu mencapai progress yang sama
            ->limit($limit)
            ->get();

        return $progresses->map(function ($progress, $index) {
            return [
                'rank' => $index + 1,
                'mitra' => [
                    'id' => $progress->mitra->id,
                    'name' => $progress->mitra->name,
                    'phone' => $progress->mitra->phone,
                    'address' => $progress->mitra->address
                ],
                'progress' => [
                    'current_quantity' => $progress->current_quantity,
                    'target_quantity' => $progress->target_quantity,
                    'remaining_quantity' => $progress->remaining_quantity,
                    'progress_percentage' => $progress->progress_percentage,
                    'status' => $progress->status,
                    'status_name' => $progress->status_name,
                    'completed_at' => $progress->completed_at?->format('d/m/Y H:i')
                ],
                'statistics' => $progress->getStatistics(),
                'estimated_completion' => $progress->getEstimatedCompletionDate()?->format('d/m/Y')
            ];
        })->toArray();
    }

    /**
     * Get statistik reward untuk owner
     */
    public function getRewardStatsForOwner(int $ownerId): array
    {
        $rewards = Reward::forOwner($ownerId)->get();
        
        $totalRewards = $rewards->count();
        $activeRewards = $rewards->where('status', 'active')->count();
        $completedRewards = $rewards->where('status', 'completed')->count();
        $expiredRewards = $rewards->where('status', 'expired')->count();

        // Total participants across all rewards
        $totalParticipants = RewardProgress::whereIn('reward_id', $rewards->pluck('id'))->count();
        $completedParticipants = RewardProgress::whereIn('reward_id', $rewards->pluck('id'))
            ->where('status', 'completed')->count();

        // Reward dengan partisipasi terbanyak
        $mostPopularReward = $rewards->sortByDesc('total_participants')->first();

        // Reward dengan completion rate tertinggi
        $bestPerformingReward = $rewards->filter(function ($reward) {
            return $reward->total_participants > 0;
        })->sortByDesc(function ($reward) {
            return $reward->completed_participants / $reward->total_participants;
        })->first();

        return [
            'total_rewards' => $totalRewards,
            'active_rewards' => $activeRewards,
            'completed_rewards' => $completedRewards,
            'expired_rewards' => $expiredRewards,
            'total_participants' => $totalParticipants,
            'completed_participants' => $completedParticipants,
            'overall_completion_rate' => $totalParticipants > 0 
                ? round(($completedParticipants / $totalParticipants) * 100, 2) 
                : 0,
            'most_popular_reward' => $mostPopularReward ? [
                'id' => $mostPopularReward->id,
                'name' => $mostPopularReward->name,
                'participants' => $mostPopularReward->total_participants
            ] : null,
            'best_performing_reward' => $bestPerformingReward ? [
                'id' => $bestPerformingReward->id,
                'name' => $bestPerformingReward->name,
                'completion_rate' => round(($bestPerformingReward->completed_participants / $bestPerformingReward->total_participants) * 100, 2)
            ] : null
        ];
    }

    /**
     * Update status reward yang kedaluwarsa
     */
    public function updateExpiredRewards(): int
    {
        $expiredCount = 0;

        $expiredRewards = Reward::where('status', 'active')
            ->where('end_date', '<', now()->toDateString())
            ->get();

        foreach ($expiredRewards as $reward) {
            if ($reward->updateStatusIfNeeded()) {
                $expiredCount++;
            }
        }

        Log::info('Updated expired rewards', ['count' => $expiredCount]);

        return $expiredCount;
    }

    /**
     * Get progress detail untuk mitra tertentu
     */
    public function getMitraRewardProgress(int $mitraId, int $ownerId): array
    {
        $progresses = RewardProgress::with(['reward.category', 'reward.product'])
            ->whereHas('reward', function ($q) use ($ownerId) {
                $q->forOwner($ownerId);
            })
            ->where('mitra_id', $mitraId)
            ->orderBy('created_at', 'desc')
            ->get();

        return $progresses->map(function ($progress) {
            return [
                'reward' => [
                    'id' => $progress->reward->id,
                    'name' => $progress->reward->name,
                    'description' => $progress->reward->description,
                    'category' => $progress->reward->category->name,
                    'product' => $progress->reward->product?->name,
                    'target_quantity' => $progress->reward->target_quantity,
                    'end_date' => $progress->reward->end_date->format('d/m/Y'),
                    'remaining_days' => $progress->reward->remaining_days
                ],
                'progress' => [
                    'current_quantity' => $progress->current_quantity,
                    'target_quantity' => $progress->target_quantity,
                    'remaining_quantity' => $progress->remaining_quantity,
                    'progress_percentage' => $progress->progress_percentage,
                    'status' => $progress->status,
                    'status_name' => $progress->status_name,
                    'completed_at' => $progress->completed_at?->format('d/m/Y H:i')
                ],
                'statistics' => $progress->getStatistics(),
                'purchase_history' => $progress->getFormattedPurchaseHistory(),
                'estimated_completion' => $progress->getEstimatedCompletionDate()?->format('d/m/Y')
            ];
        })->toArray();
    }

    /**
     * Validasi data reward sebelum disimpan
     */
    public function validateRewardData(array $data): array
    {
        $errors = [];

        // Validasi tanggal
        $startDate = Carbon::parse($data['start_date']);
        $endDate = Carbon::parse($data['end_date']);

        if ($startDate->gte($endDate)) {
            $errors[] = 'Tanggal mulai harus lebih kecil dari tanggal berakhir';
        }

        if ($startDate->lt(Carbon::today())) {
            $errors[] = 'Tanggal mulai tidak boleh di masa lalu';
        }

        // Validasi target quantity
        if ($data['target_quantity'] <= 0) {
            $errors[] = 'Target penjualan harus lebih besar dari 0';
        }

        return $errors;
    }
}
