<?php

namespace Database\Seeders;

use App\Models\User;
use App\Models\Category;
use App\Models\Product;
use App\Models\Mitra;
use App\Models\Transaction;
use App\Models\TransactionItem;
use App\Services\TransactionLogService;
use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\Hash;

class TransactionLogTestSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Buat Owner
        $owner = User::create([
            'name' => 'Owner Toko',
            'email' => '<EMAIL>',
            'phone' => '628111000001',
            'role' => 'owner',
            'password' => Hash::make('password'),
        ]);

        // Buat Admin Gudang 1
        $adminGudang1 = User::create([
            'name' => 'Admin Gudang A',
            'email' => '<EMAIL>',
            'phone' => '628222000001',
            'role' => 'admin_gudang',
            'owner_id' => $owner->id,
            'password' => Hash::make('password'),
        ]);

        // Buat Admin Gudang 2
        $adminGudang2 = User::create([
            'name' => 'Admin Gudang B',
            'email' => '<EMAIL>',
            'phone' => '628222000002',
            'role' => 'admin_gudang',
            'owner_id' => $owner->id,
            'password' => Hash::make('password'),
        ]);

        // Buat Sales
        $sales = User::create([
            'name' => 'Sales Toko',
            'email' => '<EMAIL>',
            'phone' => '628333000001',
            'role' => 'sales',
            'owner_id' => $owner->id,
            'password' => Hash::make('password'),
        ]);

        // Buat Mitra
        $mitra = Mitra::create([
            'name' => 'PT Tani Sejahtera',
            'phone' => '628444000001',
            'address' => 'Jl. Pertanian No. 123',
            'credit_limit' => 5000000,
            'current_debt' => 0,
            'status' => 'active',
            'owner_id' => $owner->id,
            'created_by' => $owner->id,
        ]);

        // Buat Kategori dan Produk
        $category = Category::create([
            'name' => 'Pupuk',
            'description' => 'Kategori untuk produk pupuk',
            'created_by' => $owner->id,
        ]);

        $product = Product::create([
            'name' => 'Pupuk NPK 16-16-16',
            'category_id' => $category->id,
            'price' => 125000,
            'hpp' => 100000,
            'stock' => 100,
            'unit' => 'kg',
            'description' => 'Pupuk NPK berkualitas tinggi',
            'created_by' => $owner->id,
        ]);

        // Buat beberapa transaksi dengan status berbeda untuk demo logging
        
        // Transaksi 1: Sudah approved dan shipped (akan ada log approve + ship)
        $transaction1 = Transaction::create([
            'mitra_id' => $mitra->id,
            'sales_id' => $sales->id,
            'subtotal' => 1250000,
            'total' => 1250000,
            'payment_method' => 'cash',
            'status' => 'shipped',
            'approved_by' => $adminGudang1->id,
        ]);

        TransactionItem::create([
            'transaction_id' => $transaction1->id,
            'product_id' => $product->id,
            'product_name' => $product->name,
            'price' => $product->price,
            'quantity' => 10,
            'total' => 1250000,
        ]);

        // Transaksi 2: Ditolak (akan ada log reject)
        $transaction2 = Transaction::create([
            'mitra_id' => $mitra->id,
            'sales_id' => $sales->id,
            'subtotal' => 625000,
            'total' => 625000,
            'payment_method' => 'credit',
            'status' => 'rejected',
            'rejection_reason' => 'Stok tidak mencukupi',
        ]);

        TransactionItem::create([
            'transaction_id' => $transaction2->id,
            'product_id' => $product->id,
            'product_name' => $product->name,
            'price' => $product->price,
            'quantity' => 5,
            'total' => 625000,
        ]);

        // Transaksi 3: Pending gudang (untuk demo approve)
        $transaction3 = Transaction::create([
            'mitra_id' => $mitra->id,
            'sales_id' => $sales->id,
            'subtotal' => 375000,
            'total' => 375000,
            'payment_method' => 'cash',
            'status' => 'pending_gudang',
        ]);

        TransactionItem::create([
            'transaction_id' => $transaction3->id,
            'product_id' => $product->id,
            'product_name' => $product->name,
            'price' => $product->price,
            'quantity' => 3,
            'total' => 375000,
        ]);

        // Buat log untuk transaksi yang sudah selesai
        $logService = new TransactionLogService();

        // Log untuk transaksi 1 (approved dan shipped)
        $logService->logApproveGudang($transaction1, $adminGudang1, [
            'items_count' => 1,
            'payment_method' => 'cash'
        ]);

        $logService->logShip($transaction1, $adminGudang2, 'RESI-TXN001-2024');

        // Log untuk transaksi 2 (rejected)
        $logService->logReject($transaction2, $adminGudang1, 'Stok tidak mencukupi');

        $this->command->info('Test data untuk sistem logging transaksi berhasil dibuat!');
        $this->command->info('');
        $this->command->info('Login credentials:');
        $this->command->info('Owner: <EMAIL> / password');
        $this->command->info('Admin Gudang A: <EMAIL> / password');
        $this->command->info('Admin Gudang B: <EMAIL> / password');
        $this->command->info('Sales: <EMAIL> / password');
        $this->command->info('');
        $this->command->info('Data yang tersedia:');
        $this->command->info('- 3 transaksi dengan status berbeda');
        $this->command->info('- Log aktivitas admin gudang sudah tersedia');
        $this->command->info('- Owner dapat memantau aktivitas admin gudang melalui API');
        $this->command->info('');
        $this->command->info('API Endpoints untuk testing:');
        $this->command->info('- GET /api/transaction-logs (semua log)');
        $this->command->info('- GET /api/transaction-logs/stats (statistik aktivitas)');
        $this->command->info('- GET /api/transaction-logs/recent (aktivitas terbaru)');
    }
}
