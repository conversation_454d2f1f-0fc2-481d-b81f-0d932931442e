<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class Product extends Model
{
    use HasFactory;

    protected $fillable = ['name', 'category_id', 'price', 'hpp', 'stock', 'unit_id', 'unit_legacy', 'description', 'created_by'];

    public function category()
    {
        return $this->belongsTo(Category::class);
    }

    /**
     * Relasi ke Unit
     */
    public function unit()
    {
        return $this->belongsTo(Unit::class);
    }

    /**
     * Accessor untuk unit name (backward compatibility)
     */
    public function getUnitNameAttribute(): string
    {
        if ($this->unit) {
            return $this->unit->name;
        }

        // Fallback ke unit_legacy jika unit_id tidak ada
        return $this->unit_legacy ?? '';
    }

    /**
     * Accessor untuk unit display text
     */
    public function getUnitDisplayAttribute(): string
    {
        if ($this->unit) {
            return $this->unit->display_text;
        }

        return $this->unit_legacy ?? '';
    }
}
