<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use App\Models\User;
use App\Models\Category;
use App\Models\Product;
use App\Models\Unit;
use App\Models\Mitra;
use App\Models\Transaction;
use App\Models\TransactionItem;
use App\Models\Bonus;
use App\Models\BonusClaim;
use App\Models\Reward;
use App\Models\RewardProgress;
use Carbon\Carbon;
use Illuminate\Support\Facades\Hash;

class AdminAccessTestSeeder extends Seeder
{
    /**
     * Run the database seeder untuk testing akses admin gudang, bonus, dan reward
     */
    public function run(): void
    {
        $this->command->info('Creating test data for admin access control...');

        // 1. Buat Users dengan berbagai role
        $this->createUsers();

        // 2. Buat data master (categories, products, units, mitras)
        $this->createMasterData();

        // 3. Buat transactions untuk testing
        $this->createTransactions();

        // 4. Buat bonus dan claims untuk testing
        $this->createBonusData();

        // 5. Buat rewards dan progress untuk testing
        $this->createRewardData();

        $this->command->info('Admin access test data created successfully!');
        $this->displayCredentials();
    }

    private function createUsers()
    {
        $this->command->info('Creating users with different roles...');

        // Superadmin
        $superadmin = User::firstOrCreate(
            ['email' => '<EMAIL>'],
            [
                'name' => 'Super Admin',
                'password' => Hash::make('password'),
                'role' => 'superadmin',
                'phone' => '081234567890'
            ]
        );

        // Owner 1
        $owner1 = User::firstOrCreate(
            ['email' => '<EMAIL>'],
            [
                'name' => 'Owner Pertama',
                'password' => Hash::make('password'),
                'role' => 'owner',
                'phone' => '081234567891'
            ]
        );

        // Owner 2
        $owner2 = User::firstOrCreate(
            ['email' => '<EMAIL>'],
            [
                'name' => 'Owner Kedua',
                'password' => Hash::make('password'),
                'role' => 'owner',
                'phone' => '081234567892'
            ]
        );

        // Admin Gudang untuk Owner 1
        $adminGudang1 = User::firstOrCreate(
            ['email' => '<EMAIL>'],
            [
                'name' => 'Admin Gudang Owner 1',
                'password' => Hash::make('password'),
                'role' => 'admin_gudang',
                'phone' => '081234567893',
                'owner_id' => $owner1->id
            ]
        );

        // Admin Gudang untuk Owner 2
        $adminGudang2 = User::firstOrCreate(
            ['email' => '<EMAIL>'],
            [
                'name' => 'Admin Gudang Owner 2',
                'password' => Hash::make('password'),
                'role' => 'admin_gudang',
                'phone' => '081234567894',
                'owner_id' => $owner2->id
            ]
        );

        // Sales untuk Owner 1
        $sales1 = User::firstOrCreate(
            ['email' => '<EMAIL>'],
            [
                'name' => 'Sales Owner 1',
                'password' => Hash::make('password'),
                'role' => 'sales',
                'phone' => '081234567895',
                'owner_id' => $owner1->id
            ]
        );

        // Sales untuk Owner 2
        $sales2 = User::firstOrCreate(
            ['email' => '<EMAIL>'],
            [
                'name' => 'Sales Owner 2',
                'password' => Hash::make('password'),
                'role' => 'sales',
                'phone' => '081234567896',
                'owner_id' => $owner2->id
            ]
        );
    }

    private function createMasterData()
    {
        $this->command->info('Creating master data...');

        $owner1 = User::where('email', '<EMAIL>')->first();
        $owner2 = User::where('email', '<EMAIL>')->first();

        // Units
        $unitKg = Unit::firstOrCreate(
            ['name' => 'kg'],
            [
                'content' => '1 kilogram',
                'quantity' => 1,
                'base_unit' => 'kg',
                'description' => 'Kilogram',
                'status' => 'active'
            ]
        );

        $unitPcs = Unit::firstOrCreate(
            ['name' => 'pcs'],
            [
                'content' => '1 piece',
                'quantity' => 1,
                'base_unit' => 'pcs',
                'description' => 'Pieces',
                'status' => 'active'
            ]
        );

        // Categories untuk Owner 1
        $categoryDetergent1 = Category::firstOrCreate(
            ['name' => 'Detergent Owner 1', 'created_by' => $owner1->id],
            ['description' => 'Produk detergent untuk Owner 1']
        );

        $categoryMakanan1 = Category::firstOrCreate(
            ['name' => 'Makanan Owner 1', 'created_by' => $owner1->id],
            ['description' => 'Produk makanan untuk Owner 1']
        );

        // Categories untuk Owner 2
        $categoryDetergent2 = Category::firstOrCreate(
            ['name' => 'Detergent Owner 2', 'created_by' => $owner2->id],
            ['description' => 'Produk detergent untuk Owner 2']
        );

        // Products untuk Owner 1
        Product::firstOrCreate(
            ['name' => 'Rinso Anti Noda Owner 1', 'category_id' => $categoryDetergent1->id],
            [
                'price' => 15000,
                'hpp' => 12000,
                'stock' => 500,
                'unit_id' => $unitPcs->id,
                'created_by' => $owner1->id
            ]
        );

        Product::firstOrCreate(
            ['name' => 'Indomie Goreng Owner 1', 'category_id' => $categoryMakanan1->id],
            [
                'price' => 3500,
                'hpp' => 2800,
                'stock' => 1000,
                'unit_id' => $unitPcs->id,
                'created_by' => $owner1->id
            ]
        );

        // Products untuk Owner 2
        Product::firstOrCreate(
            ['name' => 'Surf Excel Owner 2', 'category_id' => $categoryDetergent2->id],
            [
                'price' => 18000,
                'hpp' => 14000,
                'stock' => 300,
                'unit_id' => $unitPcs->id,
                'created_by' => $owner2->id
            ]
        );

        // Mitras untuk Owner 1
        Mitra::firstOrCreate(
            ['name' => 'Toko Berkah Owner 1', 'owner_id' => $owner1->id],
            [
                'phone' => '081234567801',
                'address' => 'Jl. Berkah No. 1',
                'credit_limit' => 5000000,
                'current_debt' => 0,
                'status' => 'active'
            ]
        );

        // Mitras untuk Owner 2
        Mitra::firstOrCreate(
            ['name' => 'Toko Makmur Owner 2', 'owner_id' => $owner2->id],
            [
                'phone' => '081234567802',
                'address' => 'Jl. Makmur No. 2',
                'credit_limit' => 3000000,
                'current_debt' => 0,
                'status' => 'active'
            ]
        );
    }

    private function createTransactions()
    {
        $this->command->info('Creating transactions for testing...');

        $owner1 = User::where('email', '<EMAIL>')->first();
        $sales1 = User::where('email', '<EMAIL>')->first();
        $mitra1 = Mitra::where('name', 'Toko Berkah Owner 1')->first();
        $product1 = Product::where('name', 'Rinso Anti Noda Owner 1')->first();

        // Transaction pending approval owner
        $transaction1 = Transaction::create([
            'mitra_id' => $mitra1->id,
            'sales_id' => $sales1->id,
            'subtotal' => 150000,
            'discount_type' => 'total',
            'discount_value' => 7500,
            'discount_reason' => 'Diskon khusus untuk testing',
            'total' => 142500,
            'payment_method' => 'credit',
            'status' => 'pending_owner',
            'due_date' => now()->addDays(30)
        ]);

        TransactionItem::create([
            'transaction_id' => $transaction1->id,
            'product_id' => $product1->id,
            'product_name' => $product1->name,
            'price' => 15000,
            'quantity' => 10,
            'discount' => 0,
            'total' => 150000
        ]);

        // Transaction pending approval gudang
        $transaction2 = Transaction::create([
            'mitra_id' => $mitra1->id,
            'sales_id' => $sales1->id,
            'subtotal' => 105000,
            'discount_type' => 'none',
            'discount_value' => 0,
            'total' => 105000,
            'payment_method' => 'credit',
            'status' => 'pending_gudang',
            'approved_by' => $owner1->id,
            'due_date' => now()->addDays(30)
        ]);

        TransactionItem::create([
            'transaction_id' => $transaction2->id,
            'product_id' => $product1->id,
            'product_name' => $product1->name,
            'price' => 15000,
            'quantity' => 7,
            'discount' => 0,
            'total' => 105000
        ]);
    }

    private function createBonusData()
    {
        $this->command->info('Creating bonus data for testing...');

        $owner1 = User::where('email', '<EMAIL>')->first();
        $owner2 = User::where('email', '<EMAIL>')->first();
        $categoryDetergent1 = Category::where('name', 'Detergent Owner 1')->first();
        $categoryDetergent2 = Category::where('name', 'Detergent Owner 2')->first();
        $product1 = Product::where('name', 'Rinso Anti Noda Owner 1')->first();
        $product2 = Product::where('name', 'Surf Excel Owner 2')->first();
        $mitra1 = Mitra::where('name', 'Toko Berkah Owner 1')->first();
        $mitra2 = Mitra::where('name', 'Toko Makmur Owner 2')->first();

        // Bonus aktif untuk Owner 1
        $bonus1 = Bonus::firstOrCreate(
            ['name' => 'Bonus Rinso 50 Pcs Owner 1', 'created_by' => $owner1->id],
            [
                'category_id' => $categoryDetergent1->id,
                'product_id' => $product1->id,
                'minimum_quantity' => 50,
                'bonus_description' => 'Gratis 5 pcs Rinso untuk pembelian 50 pcs',
                'type' => 'quota',
                'quota_total' => 100,
                'quota_used' => 25,
                'status' => 'active'
            ]
        );

        // Bonus tidak aktif untuk Owner 1 (untuk testing perubahan status)
        $bonus2 = Bonus::firstOrCreate(
            ['name' => 'Bonus Rinso 100 Pcs Owner 1', 'created_by' => $owner1->id],
            [
                'category_id' => $categoryDetergent1->id,
                'product_id' => $product1->id,
                'minimum_quantity' => 100,
                'bonus_description' => 'Gratis 10 pcs Rinso untuk pembelian 100 pcs',
                'type' => 'periode',
                'start_date' => Carbon::now()->subDays(30),
                'end_date' => Carbon::now()->addDays(30),
                'status' => 'inactive'
            ]
        );

        // Bonus untuk Owner 2
        $bonus3 = Bonus::firstOrCreate(
            ['name' => 'Bonus Surf Excel Owner 2', 'created_by' => $owner2->id],
            [
                'category_id' => $categoryDetergent2->id,
                'product_id' => $product2->id,
                'minimum_quantity' => 30,
                'bonus_description' => 'Gratis 3 pcs Surf Excel untuk pembelian 30 pcs',
                'type' => 'quota',
                'quota_total' => 50,
                'quota_used' => 10,
                'status' => 'active'
            ]
        );

        // Buat beberapa bonus claims untuk testing
        $transaction1 = Transaction::first();
        if ($transaction1) {
            BonusClaim::firstOrCreate(
                ['bonus_id' => $bonus1->id, 'transaction_id' => $transaction1->id],
                [
                    'mitra_id' => $mitra1->id,
                    'quantity_purchased' => 60,
                    'bonus_received' => 'Gratis 5 pcs Rinso untuk pembelian 50 pcs',
                    'status' => 'pending'
                ]
            );
        }
    }

    private function createRewardData()
    {
        $this->command->info('Creating reward data for testing...');

        $owner1 = User::where('email', '<EMAIL>')->first();
        $owner2 = User::where('email', '<EMAIL>')->first();
        $categoryDetergent1 = Category::where('name', 'Detergent Owner 1')->first();
        $categoryMakanan1 = Category::where('name', 'Makanan Owner 1')->first();
        $categoryDetergent2 = Category::where('name', 'Detergent Owner 2')->first();
        $product1 = Product::where('name', 'Rinso Anti Noda Owner 1')->first();
        $product2 = Product::where('name', 'Indomie Goreng Owner 1')->first();
        $product3 = Product::where('name', 'Surf Excel Owner 2')->first();
        $mitra1 = Mitra::where('name', 'Toko Berkah Owner 1')->first();
        $mitra2 = Mitra::where('name', 'Toko Makmur Owner 2')->first();

        // Reward aktif untuk Owner 1
        $reward1 = Reward::firstOrCreate(
            ['name' => 'Motor Matic Rinso Owner 1', 'created_by' => $owner1->id],
            [
                'description' => 'Dapatkan motor matic Honda Beat dengan membeli Rinso minimal 1000 pcs',
                'category_id' => $categoryDetergent1->id,
                'product_id' => $product1->id,
                'target_quantity' => 1000,
                'start_date' => Carbon::now()->subDays(10),
                'end_date' => Carbon::now()->addDays(50),
                'status' => 'active',
                'terms_conditions' => 'Berlaku untuk pembelian dalam periode program',
                'reward_value' => 18000000
            ]
        );

        // Reward tidak aktif untuk Owner 1 (untuk testing perubahan status)
        $reward2 = Reward::firstOrCreate(
            ['name' => 'iPhone 15 Indomie Owner 1', 'created_by' => $owner1->id],
            [
                'description' => 'Dapatkan iPhone 15 dengan membeli Indomie minimal 5000 pcs',
                'category_id' => $categoryMakanan1->id,
                'product_id' => $product2->id,
                'target_quantity' => 5000,
                'start_date' => Carbon::now()->subDays(5),
                'end_date' => Carbon::now()->addDays(25),
                'status' => 'inactive',
                'terms_conditions' => 'Berlaku untuk pembelian dalam periode program',
                'reward_value' => 20000000
            ]
        );

        // Reward untuk Owner 2
        $reward3 = Reward::firstOrCreate(
            ['name' => 'Liburan Bali Surf Excel Owner 2', 'created_by' => $owner2->id],
            [
                'description' => 'Paket liburan ke Bali 4 hari 3 malam untuk 2 orang',
                'category_id' => $categoryDetergent2->id,
                'product_id' => $product3->id,
                'target_quantity' => 800,
                'start_date' => Carbon::now()->subDays(15),
                'end_date' => Carbon::now()->addDays(15),
                'status' => 'active',
                'terms_conditions' => 'Paket sudah termasuk tiket pesawat, hotel, dan tour guide',
                'reward_value' => 8000000
            ]
        );

        // Buat reward progress untuk testing
        RewardProgress::firstOrCreate(
            ['reward_id' => $reward1->id, 'mitra_id' => $mitra1->id],
            [
                'current_quantity' => 250,
                'target_quantity' => 1000,
                'progress_percentage' => 25.00,
                'status' => 'in_progress',
                'purchase_history' => [
                    [
                        'date' => Carbon::now()->subDays(5)->toDateString(),
                        'quantity' => 150,
                        'total_quantity' => 150,
                        'created_at' => Carbon::now()->subDays(5)->toISOString()
                    ],
                    [
                        'date' => Carbon::now()->subDays(2)->toDateString(),
                        'quantity' => 100,
                        'total_quantity' => 250,
                        'created_at' => Carbon::now()->subDays(2)->toISOString()
                    ]
                ]
            ]
        );

        RewardProgress::firstOrCreate(
            ['reward_id' => $reward3->id, 'mitra_id' => $mitra2->id],
            [
                'current_quantity' => 120,
                'target_quantity' => 800,
                'progress_percentage' => 15.00,
                'status' => 'in_progress',
                'purchase_history' => [
                    [
                        'date' => Carbon::now()->subDays(8)->toDateString(),
                        'quantity' => 120,
                        'total_quantity' => 120,
                        'created_at' => Carbon::now()->subDays(8)->toISOString()
                    ]
                ]
            ]
        );
    }

    private function displayCredentials()
    {
        $this->command->info('');
        $this->command->info('=== ADMIN ACCESS TEST CREDENTIALS ===');
        $this->command->info('');
        $this->command->info('SUPERADMIN:');
        $this->command->info('Email: <EMAIL> | Password: password');
        $this->command->info('');
        $this->command->info('OWNER 1:');
        $this->command->info('Email: <EMAIL> | Password: password');
        $this->command->info('');
        $this->command->info('OWNER 2:');
        $this->command->info('Email: <EMAIL> | Password: password');
        $this->command->info('');
        $this->command->info('ADMIN GUDANG OWNER 1:');
        $this->command->info('Email: <EMAIL> | Password: password');
        $this->command->info('');
        $this->command->info('ADMIN GUDANG OWNER 2:');
        $this->command->info('Email: <EMAIL> | Password: password');
        $this->command->info('');
        $this->command->info('SALES OWNER 1:');
        $this->command->info('Email: <EMAIL> | Password: password');
        $this->command->info('');
        $this->command->info('SALES OWNER 2:');
        $this->command->info('Email: <EMAIL> | Password: password');
        $this->command->info('');
        $this->command->info('=== TEST SCENARIOS ===');
        $this->command->info('');
        $this->command->info('1. TRANSACTION APPROVAL:');
        $this->command->info('   - <NAME_EMAIL> untuk approve transaction pending');
        $this->command->info('   - <NAME_EMAIL> untuk approve transaction yang sudah di-approve owner');
        $this->command->info('   - Cek siapa yang mengubah status di transaction logs');
        $this->command->info('');
        $this->command->info('2. BONUS MANAGEMENT:');
        $this->command->info('   - <NAME_EMAIL> untuk mengubah status bonus');
        $this->command->info('   - <NAME_EMAIL> (tidak boleh akses bonus)');
        $this->command->info('   - Cek tracking perubahan status bonus');
        $this->command->info('');
        $this->command->info('3. REWARD MANAGEMENT:');
        $this->command->info('   - <NAME_EMAIL> untuk mengubah status reward');
        $this->command->info('   - <NAME_EMAIL> (tidak boleh akses reward)');
        $this->command->info('   - Cek tracking perubahan status reward');
        $this->command->info('');
        $this->command->info('4. CROSS-OWNER ACCESS:');
        $this->command->info('   - <NAME_EMAIL> tidak boleh lihat data owner2');
        $this->command->info('   - <NAME_EMAIL> tidak boleh lihat data owner2');
        $this->command->info('   - <NAME_EMAIL> boleh lihat semua data');
    }
}
