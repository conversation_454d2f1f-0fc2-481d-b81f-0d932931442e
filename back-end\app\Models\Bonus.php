<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Carbon\Carbon;

class Bonus extends Model
{
    use HasFactory;

    protected $fillable = [
        'name',
        'category_id',
        'product_id',
        'minimum_quantity',
        'bonus_description',
        'type',
        'start_date',
        'end_date',
        'quota_total',
        'quota_used',
        'status',
        'created_by'
    ];

    protected $casts = [
        'start_date' => 'date',
        'end_date' => 'date',
        'minimum_quantity' => 'integer',
        'quota_total' => 'integer',
        'quota_used' => 'integer',
    ];

    /**
     * Relasi ke Category
     */
    public function category()
    {
        return $this->belongsTo(Category::class);
    }

    /**
     * Relasi ke Product (opsional)
     */
    public function product()
    {
        return $this->belongsTo(Product::class);
    }

    /**
     * Relasi ke User (creator)
     */
    public function creator()
    {
        return $this->belongsTo(User::class, 'created_by');
    }

    /**
     * <PERSON>lasi ke BonusClaim
     */
    public function claims()
    {
        return $this->hasMany(BonusClaim::class);
    }

    /**
     * Scope untuk bonus aktif
     */
    public function scopeActive($query)
    {
        return $query->where('status', 'active');
    }

    /**
     * Scope untuk bonus berdasarkan owner
     */
    public function scopeByOwner($query, $ownerId)
    {
        return $query->whereHas('creator', function ($q) use ($ownerId) {
            $q->where('id', $ownerId)->orWhere('owner_id', $ownerId);
        });
    }

    /**
     * Scope untuk bonus yang berlaku saat ini
     */
    public function scopeCurrentlyValid($query)
    {
        $now = Carbon::now();
        
        return $query->where('status', 'active')
            ->where(function ($q) use ($now) {
                // Untuk bonus periode: cek tanggal
                $q->where(function ($periodQuery) use ($now) {
                    $periodQuery->where('type', 'periode')
                        ->where('start_date', '<=', $now)
                        ->where('end_date', '>=', $now);
                })
                // Untuk bonus quota: cek quota tersisa
                ->orWhere(function ($quotaQuery) {
                    $quotaQuery->where('type', 'quota')
                        ->whereRaw('quota_used < quota_total');
                });
            });
    }

    /**
     * Cek apakah bonus masih valid
     */
    public function isValid(): bool
    {
        if ($this->status !== 'active') {
            return false;
        }

        if ($this->type === 'periode') {
            $now = Carbon::now();
            return $now->between($this->start_date, $this->end_date);
        }

        if ($this->type === 'quota') {
            return $this->quota_used < $this->quota_total;
        }

        return false;
    }

    /**
     * Cek apakah transaksi memenuhi syarat bonus
     */
    public function isEligibleForTransaction($transactionItems): bool
    {
        if (!$this->isValid()) {
            return false;
        }

        $totalQuantity = 0;

        foreach ($transactionItems as $item) {
            // Jika bonus untuk produk spesifik
            if ($this->product_id && $item->product_id == $this->product_id) {
                $totalQuantity += $item->quantity;
            }
            // Jika bonus untuk kategori
            elseif (!$this->product_id && $item->product && $item->product->category_id == $this->category_id) {
                $totalQuantity += $item->quantity;
            }
        }

        return $totalQuantity >= $this->minimum_quantity;
    }

    /**
     * Gunakan quota bonus
     */
    public function useQuota(): bool
    {
        if ($this->type !== 'quota') {
            return true; // Bonus periode tidak perlu quota
        }

        if ($this->quota_used >= $this->quota_total) {
            return false; // Quota habis
        }

        $this->increment('quota_used');
        
        // Update status jika quota habis
        if ($this->quota_used >= $this->quota_total) {
            $this->update(['status' => 'expired']);
        }

        return true;
    }

    /**
     * Accessor untuk sisa quota
     */
    public function getRemainingQuotaAttribute(): int
    {
        if ($this->type !== 'quota') {
            return 0;
        }

        return max(0, $this->quota_total - $this->quota_used);
    }

    /**
     * Accessor untuk status yang user-friendly
     */
    public function getStatusNameAttribute(): string
    {
        $statusNames = [
            'active' => 'Aktif',
            'inactive' => 'Tidak Aktif',
            'expired' => 'Kedaluwarsa'
        ];

        return $statusNames[$this->status] ?? $this->status;
    }

    /**
     * Accessor untuk type yang user-friendly
     */
    public function getTypeNameAttribute(): string
    {
        $typeNames = [
            'periode' => 'Berdasarkan Periode',
            'quota' => 'Berdasarkan Quota'
        ];

        return $typeNames[$this->type] ?? $this->type;
    }

    /**
     * Mutator untuk auto-update status berdasarkan tanggal
     */
    public function updateStatusBasedOnDate(): void
    {
        if ($this->type === 'periode' && $this->status === 'active') {
            $now = Carbon::now();
            
            if ($now->gt($this->end_date)) {
                $this->update(['status' => 'expired']);
            }
        }
    }
}
