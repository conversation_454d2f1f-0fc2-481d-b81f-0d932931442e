<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('bonus_claims', function (Blueprint $table) {
            $table->id();
            $table->foreignId('bonus_id')->constrained('bonuses')->cascadeOnDelete(); // Bonus yang diklaim
            $table->foreignId('transaction_id')->constrained('transactions')->cascadeOnDelete(); // Transaksi yang memenuhi syarat
            $table->foreignId('mitra_id')->constrained('mitras')->cascadeOnDelete(); // Mitra yang mendapat bonus
            $table->integer('quantity_purchased'); // Jumlah yang dibeli
            $table->text('bonus_received'); // Bonus yang diterima (copy dari bonus_description)
            $table->enum('status', ['pending', 'claimed', 'expired'])->default('pending'); // Status klaim
            $table->timestamp('claimed_at')->nullable(); // Kapan bonus diklaim
            $table->text('notes')->nullable(); // Catatan tambahan
            $table->timestamps();
            
            // Index untuk performa
            $table->index(['bonus_id', 'status']);
            $table->index(['mitra_id', 'status']);
            $table->index(['transaction_id']);
            $table->index(['claimed_at']);
            
            // Constraint: satu transaksi hanya bisa klaim satu bonus per bonus_id
            $table->unique(['bonus_id', 'transaction_id']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('bonus_claims');
    }
};
