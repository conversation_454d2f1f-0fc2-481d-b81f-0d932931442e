<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use Illuminate\Support\Facades\DB;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        // Update enum untuk menambahkan approve_owner
        DB::statement("ALTER TABLE transaction_logs MODIFY COLUMN action ENUM('approve_owner', 'approve_gudang', 'reject', 'ship', 'deliver')");
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        // Kembalikan ke enum semula
        DB::statement("ALTER TABLE transaction_logs MODIFY COLUMN action ENUM('approve_gudang', 'reject', 'ship', 'deliver')");
    }
};
