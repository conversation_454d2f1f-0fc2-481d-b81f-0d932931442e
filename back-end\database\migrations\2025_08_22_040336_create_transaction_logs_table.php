<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('transaction_logs', function (Blueprint $table) {
            $table->id();
            $table->foreignId('transaction_id')->constrained('transactions')->cascadeOnDelete();
            $table->foreignId('user_id')->constrained('users')->cascadeOnDelete(); // Admin gudang yang melakukan aksi
            $table->enum('action', ['approve_gudang', 'reject', 'ship', 'deliver']); // Jenis aksi
            $table->string('previous_status')->nullable(); // Status sebelumnya
            $table->string('new_status'); // Status baru
            $table->text('notes')->nullable(); // Catatan tambahan (misal: alasan reject, nomor resi, dll)
            $table->json('metadata')->nullable(); // Data tambahan dalam format JSON
            $table->timestamps();

            // Index untuk performa query
            $table->index(['transaction_id', 'created_at']);
            $table->index(['user_id', 'created_at']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('transaction_logs');
    }
};
