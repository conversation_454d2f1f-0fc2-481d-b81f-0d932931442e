<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('bonuses', function (Blueprint $table) {
            $table->id();
            $table->string('name'); // Nama event bonus
            $table->foreignId('category_id')->constrained('categories')->cascadeOnDelete(); // Kategori produk
            $table->foreignId('product_id')->nullable()->constrained('products')->cascadeOnDelete(); // Produk spesifik (opsional)
            $table->integer('minimum_quantity'); // Jumlah pembelian minimum
            $table->text('bonus_description'); // Deskripsi bonus (input text)
            $table->enum('type', ['periode', 'quota']); // Jenis bonus: periode atau quota
            $table->date('start_date')->nullable(); // Tanggal mulai (untuk type periode)
            $table->date('end_date')->nullable(); // Tanggal sampai (untuk type periode)
            $table->integer('quota_total')->nullable(); // Total quota (untuk type quota)
            $table->integer('quota_used')->default(0); // Quota yang sudah digunakan
            $table->enum('status', ['active', 'inactive', 'expired'])->default('active'); // Status bonus
            $table->foreignId('created_by')->constrained('users')->cascadeOnDelete(); // Owner yang membuat
            $table->timestamps();
            
            // Index untuk performa
            $table->index(['category_id', 'status']);
            $table->index(['product_id', 'status']);
            $table->index(['created_by', 'status']);
            $table->index(['start_date', 'end_date']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('bonuses');
    }
};
