<?php

namespace App\Http\Controllers;

use App\Models\TransactionLog;
use App\Services\TransactionLogService;
use Illuminate\Http\Request;

class TransactionLogController extends Controller
{
    protected $transactionLogService;

    public function __construct(TransactionLogService $transactionLogService)
    {
        $this->transactionLogService = $transactionLogService;
    }

    /**
     * Mendapatkan log aktivitas untuk owner
     */
    public function index(Request $request)
    {
        $user = $request->user();
        
        // Hanya owner dan superadmin yang bisa mengakses
        if (!$user || !in_array($user->role, ['owner', 'superadmin'])) {
            return response()->json(['message' => 'Forbidden'], 403);
        }

        $perPage = (int) $request->query('per_page', 20);
        $transactionId = $request->query('transaction_id');
        $action = $request->query('action');
        $userId = $request->query('user_id'); // Filter berdasarkan admin gudang tertentu

        $query = TransactionLog::with([
            'user:id,name,role,email',
            'transaction:id,mitra_id,total,status',
            'transaction.mitra:id,name'
        ]);

        // Scope berdasarkan role
        if ($user->role === 'owner') {
            $query->byOwnerScope($user->id);
        }
        // Superadmin bisa melihat semua log

        // Filter berdasarkan transaksi tertentu
        if ($transactionId) {
            $query->forTransaction($transactionId);
        }

        // Filter berdasarkan action
        if ($action && in_array($action, ['approve_gudang', 'reject', 'ship', 'deliver'])) {
            $query->byAction($action);
        }

        // Filter berdasarkan user (admin gudang)
        if ($userId) {
            $query->byUser($userId);
        }

        $logs = $query->orderBy('created_at', 'desc')->paginate($perPage);

        return response()->json($logs);
    }

    /**
     * Mendapatkan log untuk transaksi tertentu
     */
    public function showForTransaction(Request $request, int $transactionId)
    {
        $user = $request->user();
        
        if (!$user || !in_array($user->role, ['owner', 'superadmin', 'admin_gudang'])) {
            return response()->json(['message' => 'Forbidden'], 403);
        }

        // Untuk owner, pastikan transaksi milik mereka
        if ($user->role === 'owner') {
            $transaction = \App\Models\Transaction::with('sales')
                ->where('id', $transactionId)
                ->first();
                
            if (!$transaction) {
                return response()->json(['message' => 'Transaction not found'], 404);
            }

            $sales = $transaction->sales;
            $ownerScopeId = $sales ? ($sales->owner_id ?? $sales->id) : null;
            
            if ($ownerScopeId !== $user->id) {
                return response()->json(['message' => 'Forbidden'], 403);
            }
        }

        $logs = $this->transactionLogService->getLogsForTransaction($transactionId);

        return response()->json([
            'transaction_id' => $transactionId,
            'logs' => $logs
        ]);
    }

    /**
     * Mendapatkan statistik aktivitas admin gudang
     */
    public function stats(Request $request)
    {
        $user = $request->user();
        
        if (!$user || !in_array($user->role, ['owner', 'superadmin'])) {
            return response()->json(['message' => 'Forbidden'], 403);
        }

        $days = (int) $request->query('days', 30);
        $ownerId = $user->role === 'owner' ? $user->id : $request->query('owner_id');

        if ($user->role === 'superadmin' && !$ownerId) {
            return response()->json(['message' => 'owner_id required for superadmin'], 400);
        }

        $stats = $this->transactionLogService->getActivityStats($ownerId, $days);
        $mostActiveAdmins = $this->transactionLogService->getMostActiveAdmins($ownerId, $days);

        return response()->json([
            'stats' => $stats,
            'most_active_admins' => $mostActiveAdmins
        ]);
    }

    /**
     * Mendapatkan ringkasan aktivitas terbaru
     */
    public function recent(Request $request)
    {
        $user = $request->user();
        
        if (!$user || !in_array($user->role, ['owner', 'superadmin'])) {
            return response()->json(['message' => 'Forbidden'], 403);
        }

        $limit = (int) $request->query('limit', 10);
        $ownerId = $user->role === 'owner' ? $user->id : $request->query('owner_id');

        if ($user->role === 'superadmin' && !$ownerId) {
            return response()->json(['message' => 'owner_id required for superadmin'], 400);
        }

        $logs = $this->transactionLogService->getLogsForOwner($ownerId, $limit);

        return response()->json([
            'recent_activities' => $logs->map(function ($log) {
                return [
                    'id' => $log->id,
                    'transaction_id' => $log->transaction_id,
                    'action' => $log->action,
                    'action_name' => $log->action_name,
                    'status_name' => $log->status_name,
                    'user' => [
                        'id' => $log->user->id,
                        'name' => $log->user->name,
                        'role' => $log->user->role
                    ],
                    'transaction' => [
                        'id' => $log->transaction->id,
                        'total' => $log->transaction->total,
                        'mitra_name' => $log->transaction->mitra?->name
                    ],
                    'notes' => $log->notes,
                    'created_at' => $log->created_at->toISOString()
                ];
            })
        ]);
    }

    /**
     * Export log aktivitas ke CSV (opsional)
     */
    public function export(Request $request)
    {
        $user = $request->user();
        
        if (!$user || $user->role !== 'owner') {
            return response()->json(['message' => 'Forbidden'], 403);
        }

        $days = (int) $request->query('days', 30);
        $logs = $this->transactionLogService->getLogsForOwner($user->id, 1000); // Max 1000 records

        $csvData = [];
        $csvData[] = ['Tanggal', 'Transaksi ID', 'Admin Gudang', 'Aksi', 'Status Baru', 'Catatan'];

        foreach ($logs as $log) {
            $csvData[] = [
                $log->created_at->format('Y-m-d H:i:s'),
                'TXN-' . $log->transaction_id,
                $log->user->name,
                $log->action_name,
                $log->status_name,
                $log->notes
            ];
        }

        $filename = 'transaction_logs_' . now()->format('Y-m-d') . '.csv';
        
        $headers = [
            'Content-Type' => 'text/csv',
            'Content-Disposition' => 'attachment; filename="' . $filename . '"',
        ];

        $callback = function() use ($csvData) {
            $file = fopen('php://output', 'w');
            foreach ($csvData as $row) {
                fputcsv($file, $row);
            }
            fclose($file);
        };

        return response()->stream($callback, 200, $headers);
    }
}
