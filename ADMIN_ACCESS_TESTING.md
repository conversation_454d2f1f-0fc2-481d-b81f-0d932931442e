# Testing Guide: Admin Access Control & Status Tracking

## Overview
Panduan testing untuk memastikan sistem akses admin gudang, bonus, dan reward berfungsi dengan benar, termasuk tracking siapa yang mengubah status.

## Test Data Setup

Jalankan seeder untuk membuat data testing:
```bash
php artisan db:seed --class=AdminAccessTestSeeder
```

## Test Accounts

### Superadmin
- **Email**: <EMAIL>
- **Password**: password
- **Akses**: Semua data dari semua owner

### Owner 1
- **Email**: <EMAIL>
- **Password**: password
- **Akses**: Data milik owner 1 saja

### Owner 2
- **Email**: <EMAIL>
- **Password**: password
- **Akses**: Data milik owner 2 saja

### Admin Gudang Owner 1
- **Email**: <EMAIL>
- **Password**: password
- **Akses**: Data owner 1, tidak bisa akses bonus/reward

### Admin Gudang Owner 2
- **Email**: <EMAIL>
- **Password**: password
- **Akses**: Data owner 2, tidak bisa akses bonus/reward

### Sales Owner 1
- **Email**: <EMAIL>
- **Password**: password
- **Akses**: Buat transaksi untuk owner 1

### Sales Owner 2
- **Email**: <EMAIL>
- **Password**: password
- **Akses**: Buat transaksi untuk owner 2

## Test Scenarios

### 1. Transaction Approval & Tracking

#### Test 1.1: Owner Approval
1. Login sebagai **<EMAIL>**
2. Buka halaman **Transaction List**
3. Cari transaksi dengan status "Menunggu Persetujuan Owner"
4. Klik tombol **Approve**
5. **Expected**: Status berubah menjadi "Menunggu Persetujuan Gudang"
6. **Verify**: Cek transaction logs untuk melihat siapa yang approve

#### Test 1.2: Admin Gudang Approval
1. Login sebagai **<EMAIL>**
2. Buka halaman **Transaction List**
3. Cari transaksi dengan status "Menunggu Persetujuan Gudang"
4. Klik tombol **Approve Gudang**
5. **Expected**: Status berubah menjadi "Disetujui"
6. **Verify**: Cek transaction logs untuk melihat admin gudang yang approve

#### Test 1.3: Cross-Owner Access
1. Login sebagai **<EMAIL>**
2. **Expected**: Tidak bisa melihat transaksi milik owner 2
3. Login sebagai **<EMAIL>**
4. **Expected**: Tidak bisa melihat transaksi milik owner 2
5. Login sebagai **<EMAIL>**
6. **Expected**: Bisa melihat semua transaksi

### 2. Bonus Management Access Control

#### Test 2.1: Owner Access
1. Login sebagai **<EMAIL>**
2. Buka halaman **Bonus Management**
3. **Expected**: Bisa melihat dan mengelola bonus milik owner 1
4. Coba ubah status bonus dari "inactive" ke "active"
5. **Expected**: Berhasil mengubah status
6. **Verify**: Cek siapa yang mengubah status di database

#### Test 2.2: Admin Gudang Restriction
1. Login sebagai **<EMAIL>**
2. Coba akses halaman **Bonus Management**
3. **Expected**: Tidak ada menu bonus atau mendapat error 403 Forbidden
4. Coba akses langsung URL `/api/bonuses`
5. **Expected**: Mendapat error 403 Forbidden

#### Test 2.3: Cross-Owner Bonus Access
1. Login sebagai **<EMAIL>**
2. **Expected**: Hanya bisa melihat bonus yang dibuat oleh owner 1
3. **Expected**: Tidak bisa melihat bonus milik owner 2

### 3. Reward Management Access Control

#### Test 3.1: Owner Access
1. Login sebagai **<EMAIL>**
2. Buka halaman **Reward Management**
3. **Expected**: Bisa melihat dan mengelola reward milik owner 1
4. Coba ubah status reward dari "inactive" ke "active"
5. **Expected**: Berhasil mengubah status
6. **Verify**: Cek siapa yang mengubah status di database

#### Test 3.2: Admin Gudang Restriction
1. Login sebagai **<EMAIL>**
2. Coba akses halaman **Reward Management**
3. **Expected**: Tidak ada menu reward atau mendapat error 403 Forbidden
4. Coba akses langsung URL `/api/rewards`
5. **Expected**: Mendapat error 403 Forbidden

#### Test 3.3: Cross-Owner Reward Access
1. Login sebagai **<EMAIL>**
2. **Expected**: Hanya bisa melihat reward yang dibuat oleh owner 1
3. **Expected**: Tidak bisa melihat reward milik owner 2

### 4. Database Verification

#### Check Transaction Logs
```sql
SELECT 
    tl.id,
    t.id as transaction_id,
    u.name as user_name,
    u.role,
    tl.action,
    tl.previous_status,
    tl.new_status,
    tl.notes,
    tl.created_at
FROM transaction_logs tl
JOIN transactions t ON tl.transaction_id = t.id
JOIN users u ON tl.user_id = u.id
ORDER BY tl.created_at DESC;
```

#### Check Bonus Ownership
```sql
SELECT 
    b.id,
    b.name,
    b.status,
    u.name as created_by_name,
    u.role as created_by_role
FROM bonuses b
JOIN users u ON b.created_by = u.id
ORDER BY b.created_at DESC;
```

#### Check Reward Ownership
```sql
SELECT 
    r.id,
    r.name,
    r.status,
    u.name as created_by_name,
    u.role as created_by_role
FROM rewards r
JOIN users u ON r.created_by = u.id
ORDER BY r.created_at DESC;
```

## Expected Results

### ✅ Access Control
- **Owner**: Bisa akses semua fitur untuk data miliknya
- **Admin Gudang**: Hanya bisa approve transaksi, tidak bisa akses bonus/reward
- **Sales**: Hanya bisa buat transaksi
- **Superadmin**: Bisa akses semua data

### ✅ Status Tracking
- Setiap perubahan status tercatat dengan user yang melakukan
- Transaction logs mencatat siapa yang approve/reject
- Bonus dan reward changes tercatat di audit logs

### ✅ Data Isolation
- Owner 1 tidak bisa lihat data Owner 2
- Admin Gudang 1 tidak bisa lihat data Owner 2
- Superadmin bisa lihat semua data

## Troubleshooting

### Issue: 403 Forbidden
- **Cause**: User tidak memiliki akses ke resource
- **Solution**: Pastikan login dengan role yang benar

### Issue: Data tidak muncul
- **Cause**: Data filtering berdasarkan owner
- **Solution**: Pastikan login dengan owner yang benar

### Issue: Status tidak berubah
- **Cause**: Validation error atau permission issue
- **Solution**: Cek console browser untuk error details

## API Testing

### Test Bonus API Access
```bash
# Owner access (should work)
curl -H "Authorization: Bearer {owner_token}" \
     http://localhost:8000/api/bonuses

# Admin gudang access (should fail)
curl -H "Authorization: Bearer {gudang_token}" \
     http://localhost:8000/api/bonuses
```

### Test Reward API Access
```bash
# Owner access (should work)
curl -H "Authorization: Bearer {owner_token}" \
     http://localhost:8000/api/rewards

# Admin gudang access (should fail)
curl -H "Authorization: Bearer {gudang_token}" \
     http://localhost:8000/api/rewards
```

### Test Transaction Approval
```bash
# Approve transaction
curl -X POST \
     -H "Authorization: Bearer {token}" \
     -H "Content-Type: application/json" \
     http://localhost:8000/api/transactions/{id}/approve-owner
```
