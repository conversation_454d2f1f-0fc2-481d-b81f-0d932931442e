<?php

namespace App\Http\Controllers;

use App\Models\StockLog;
use Illuminate\Http\Request;

class StockLogController extends Controller
{
    public function index(Request $request)
    {
        $user = $request->user();
        $query = StockLog::with('product')
            ->join('products', 'stock_logs.product_id', '=', 'products.id')
            ->join('users as creator', 'products.created_by', '=', 'creator.id')
            ->select('stock_logs.*');

        if ($user && $user->role !== 'superadmin') {
            $ownerScopeId = $user->role === 'owner' ? $user->id : $user->owner_id;
            if ($ownerScopeId) {
                // Stock log yang bisa dilihat:
                // 1. Dari produk yang dibuat oleh owner atau timnya
                // 2. Dari produk yang dibuat oleh superadmin
                $query->where(function ($q) use ($ownerScopeId) {
                    $q->where(function ($ownerScope) use ($ownerScopeId) {
                        $ownerScope->where('creator.id', $ownerScopeId)
                            ->orWhere('creator.owner_id', $ownerScopeId);
                    })->orWhere('creator.role', 'superadmin');
                });
            } else {
                // Fallback: stock log dari produk user sendiri + produk superadmin
                $query->where(function ($q) use ($user) {
                    $q->where('products.created_by', $user->id)
                        ->orWhere('creator.role', 'superadmin');
                });
            }
        }

        if ($request->has('product_id')) {
            $query->where('stock_logs.product_id', $request->query('product_id'));
        }
        return $query->orderBy('products.name', 'asc')->paginate(50);
    }
}
