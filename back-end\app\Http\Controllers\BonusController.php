<?php

namespace App\Http\Controllers;

use App\Models\Bonus;
use App\Models\BonusClaim;
use App\Models\Category;
use App\Models\Product;
use App\Services\BonusService;
use Illuminate\Http\Request;

class BonusController extends Controller
{
    protected $bonusService;

    public function __construct(BonusService $bonusService)
    {
        $this->bonusService = $bonusService;
    }

    /**
     * Daftar bonus untuk owner
     */
    public function index(Request $request)
    {
        $user = $request->user();
        
        // Hanya owner dan superadmin yang bisa mengakses
        if (!$user || !in_array($user->role, ['owner', 'superadmin'])) {
            return response()->json(['message' => 'Forbidden'], 403);
        }

        $perPage = (int) $request->query('per_page', 20);
        $status = $request->query('status');
        $type = $request->query('type');

        $query = Bonus::with(['category:id,name', 'product:id,name', 'creator:id,name']);

        // Scope berdasarkan role
        if ($user->role === 'owner') {
            $query->byOwner($user->id);
        }
        // Superadmin bisa melihat semua bonus

        // Filter berdasarkan status
        if ($status && in_array($status, ['active', 'inactive', 'expired'])) {
            $query->where('status', $status);
        }

        // Filter berdasarkan type
        if ($type && in_array($type, ['periode', 'quota'])) {
            $query->where('type', $type);
        }

        $bonuses = $query->orderBy('created_at', 'desc')->paginate($perPage);

        return response()->json($bonuses);
    }

    /**
     * Buat bonus baru
     */
    public function store(Request $request)
    {
        $user = $request->user();
        
        if (!$user || !in_array($user->role, ['owner', 'superadmin'])) {
            return response()->json(['message' => 'Forbidden'], 403);
        }

        $data = $request->validate([
            'name' => ['required', 'string', 'max:255'],
            'category_id' => ['required', 'exists:categories,id'],
            'product_id' => ['nullable', 'exists:products,id'],
            'minimum_quantity' => ['required', 'integer', 'min:1'],
            'bonus_description' => ['required', 'string'],
            'type' => ['required', 'in:periode,quota'],
            'start_date' => ['nullable', 'date'],
            'end_date' => ['nullable', 'date', 'after:start_date'],
            'quota_total' => ['nullable', 'integer', 'min:1'],
            'status' => ['required', 'in:active,inactive']
        ]);

        // Validasi data bonus
        $validationErrors = $this->bonusService->validateBonusData($data);
        if (!empty($validationErrors)) {
            return response()->json([
                'message' => 'Validation failed',
                'errors' => $validationErrors
            ], 422);
        }

        // Validasi kategori dan produk milik owner yang sama (untuk owner)
        if ($user->role === 'owner') {
            $category = Category::find($data['category_id']);
            $categoryCreator = $category ? \App\Models\User::find($category->created_by) : null;
            $categoryOwnerId = $categoryCreator ? ($categoryCreator->role === 'owner' ? $categoryCreator->id : $categoryCreator->owner_id) : null;
            
            if ($categoryOwnerId !== $user->id) {
                return response()->json(['message' => 'Category not found or not accessible'], 403);
            }

            if ($data['product_id']) {
                $product = Product::find($data['product_id']);
                $productCreator = $product ? \App\Models\User::find($product->created_by) : null;
                $productOwnerId = $productCreator ? ($productCreator->role === 'owner' ? $productCreator->id : $productCreator->owner_id) : null;
                
                if ($productOwnerId !== $user->id) {
                    return response()->json(['message' => 'Product not found or not accessible'], 403);
                }
            }
        }

        $data['created_by'] = $user->id;

        // Set default values berdasarkan type
        if ($data['type'] === 'periode') {
            $data['quota_total'] = null;
            $data['quota_used'] = 0;
        } else {
            $data['start_date'] = null;
            $data['end_date'] = null;
        }

        $bonus = Bonus::create($data);
        
        return response()->json($bonus->load(['category', 'product']), 201);
    }

    /**
     * Detail bonus
     */
    public function show(Request $request, Bonus $bonus)
    {
        $user = $request->user();
        
        if (!$user || !in_array($user->role, ['owner', 'superadmin'])) {
            return response()->json(['message' => 'Forbidden'], 403);
        }

        // Validasi akses untuk owner
        if ($user->role === 'owner') {
            $creator = $bonus->creator;
            $creatorOwnerId = $creator ? ($creator->role === 'owner' ? $creator->id : $creator->owner_id) : null;
            
            if ($creatorOwnerId !== $user->id) {
                return response()->json(['message' => 'Bonus not found'], 404);
            }
        }

        return response()->json($bonus->load(['category', 'product', 'creator:id,name']));
    }

    /**
     * Update bonus
     */
    public function update(Request $request, Bonus $bonus)
    {
        $user = $request->user();
        
        if (!$user || !in_array($user->role, ['owner', 'superadmin'])) {
            return response()->json(['message' => 'Forbidden'], 403);
        }

        // Validasi akses untuk owner
        if ($user->role === 'owner') {
            $creator = $bonus->creator;
            $creatorOwnerId = $creator ? ($creator->role === 'owner' ? $creator->id : $creator->owner_id) : null;
            
            if ($creatorOwnerId !== $user->id) {
                return response()->json(['message' => 'Bonus not found'], 404);
            }
        }

        $data = $request->validate([
            'name' => ['required', 'string', 'max:255'],
            'category_id' => ['required', 'exists:categories,id'],
            'product_id' => ['nullable', 'exists:products,id'],
            'minimum_quantity' => ['required', 'integer', 'min:1'],
            'bonus_description' => ['required', 'string'],
            'type' => ['required', 'in:periode,quota'],
            'start_date' => ['nullable', 'date'],
            'end_date' => ['nullable', 'date', 'after:start_date'],
            'quota_total' => ['nullable', 'integer', 'min:1'],
            'status' => ['required', 'in:active,inactive,expired']
        ]);

        // Validasi data bonus
        $validationErrors = $this->bonusService->validateBonusData($data);
        if (!empty($validationErrors)) {
            return response()->json([
                'message' => 'Validation failed',
                'errors' => $validationErrors
            ], 422);
        }

        // Set default values berdasarkan type
        if ($data['type'] === 'periode') {
            $data['quota_total'] = null;
        } else {
            $data['start_date'] = null;
            $data['end_date'] = null;
        }

        $bonus->update($data);
        
        return response()->json($bonus->load(['category', 'product']));
    }

    /**
     * Hapus bonus
     */
    public function destroy(Request $request, Bonus $bonus)
    {
        $user = $request->user();
        
        if (!$user || !in_array($user->role, ['owner', 'superadmin'])) {
            return response()->json(['message' => 'Forbidden'], 403);
        }

        // Validasi akses untuk owner
        if ($user->role === 'owner') {
            $creator = $bonus->creator;
            $creatorOwnerId = $creator ? ($creator->role === 'owner' ? $creator->id : $creator->owner_id) : null;
            
            if ($creatorOwnerId !== $user->id) {
                return response()->json(['message' => 'Bonus not found'], 404);
            }
        }

        // Cek apakah ada klaim yang masih pending
        $pendingClaims = BonusClaim::where('bonus_id', $bonus->id)
            ->where('status', 'pending')
            ->count();

        if ($pendingClaims > 0) {
            return response()->json([
                'message' => 'Cannot delete bonus with pending claims'
            ], 422);
        }

        $bonus->delete();
        
        return response()->json(['message' => 'Bonus deleted successfully']);
    }

    /**
     * Daftar klaim bonus
     */
    public function claims(Request $request)
    {
        $user = $request->user();
        
        if (!$user || !in_array($user->role, ['owner', 'superadmin'])) {
            return response()->json(['message' => 'Forbidden'], 403);
        }

        $perPage = (int) $request->query('per_page', 20);
        $status = $request->query('status');
        $bonusId = $request->query('bonus_id');

        $query = BonusClaim::with([
            'bonus:id,name,bonus_description',
            'transaction:id,total',
            'mitra:id,name,phone'
        ]);

        // Scope berdasarkan role
        if ($user->role === 'owner') {
            $query->byOwner($user->id);
        }

        // Filter berdasarkan status
        if ($status && in_array($status, ['pending', 'claimed', 'expired'])) {
            $query->byStatus($status);
        }

        // Filter berdasarkan bonus
        if ($bonusId) {
            $query->where('bonus_id', $bonusId);
        }

        $claims = $query->orderBy('created_at', 'desc')->paginate($perPage);

        return response()->json($claims);
    }

    /**
     * Statistik bonus
     */
    public function stats(Request $request)
    {
        $user = $request->user();
        
        if (!$user || !in_array($user->role, ['owner', 'superadmin'])) {
            return response()->json(['message' => 'Forbidden'], 403);
        }

        $days = (int) $request->query('days', 30);
        $ownerId = $user->role === 'owner' ? $user->id : $request->query('owner_id');

        if ($user->role === 'superadmin' && !$ownerId) {
            return response()->json(['message' => 'owner_id required for superadmin'], 400);
        }

        $stats = $this->bonusService->getBonusStatsForOwner($ownerId, $days);

        return response()->json($stats);
    }
}
